spring:
  application:
    name: dispatch-server

  profiles:
    active: local

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 Date 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

server:
  servlet:
    encoding:
      enabled: true
      charset: UTF-8 # 必须设置 UTF-8，避免 WebFlux 流式返回（AI 场景）会乱码问题
      force: true

#Juggle服务部署的地址
juggle:
  server-addr: http://127.0.0.1:9127
  #Juggle后台申请的令牌
  access-token: eyJ1c2VySWQiOjEsInRpbWVzdGFtcCI6MTc0MTE2ODQwNzE2N30=

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

knife4j:
  enable: true
  setting:
    language: zh_cn

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
#      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
#      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
#      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: ${cdc.info.base-package}.module.*.dal.dataobject
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

mybatis-plus-join:
  banner: false # 是否打印 mybatis plus join banner，默认true
  sub-table-logic: true # 全局启用副表逻辑删除，默认true。关闭后关联查询不会加副表逻辑删除
  ms-cache: true # 拦截器MappedStatement缓存，默认 true
  table-alias: t # 表别名(默认 t)
  logic-del-type: on # 副表逻辑删除条件的位置，支持 WHERE、ON，默认 ON

# Spring Data Redis 配置
spring:
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度
  jpa:
    #数据库生成策略，如果打开会根据entity对象生成数据库。生产环境尽量不要使用
    hibernate:
      # ddl-auto: create-drop update validate none
      ddl-auto: update
    properties:
      hibernate:
        format_sql: false
        enable_lazy_load_no_trans: true
    #是否打印Jpa生成的sql语句
    show-sql: true
    open-in-view: false

# VO 转换（数据翻译）相关
easy-trans:
  is-enable-global: true # 启用全局翻译（拦截所有 SpringMVC ResponseBody 进行自动翻译 )。如果对于性能要求很高可关闭此配置，或通过 @IgnoreTrans 忽略某个接口

--- #################### 验证码相关配置 ####################

aj:
  captcha:
    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: redis # 缓存 local/redis...
    cache-number: 1000 # local 缓存的阈值,达到这个值，清除缓存
    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    type: blockPuzzle # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: cdc # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败 5 次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制

--- #################### 消息队列相关 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  # Producer 配置项
  producer:
    group: ${spring.application.name}_PRODUCER # 生产者分组

spring:
  # Kafka 配置项，对应 KafkaProperties 配置类
  kafka:
    # Kafka Producer 配置项
    producer:
      acks: 1 # 0-不应答。1-leader 应答。all-所有 leader 和 follower 应答。
      retries: 3 # 发送失败时，重试发送的次数
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer # 消息的 value 的序列化
    # Kafka Consumer 配置项
    consumer:
      auto-offset-reset: earliest # 设置消费者分组最初的消费进度为 earliest 。可参考博客 https://blog.csdn.net/lishuangzhe7047/article/details/74530417 理解
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: '*'
    # Kafka Consumer Listener 监听器配置
    listener:
      missing-topics-fatal: false # 消费监听接口监听的主题不存在时，默认会报错。所以通过设置为 false ，解决报错

--- #################### cdc相关配置 ####################
dispatch:
  agv:
    #请求地址
    hostUrl: *************:44351 # agv调度

cdc:
  info:
    version: 1.0.0
    base-package: com.hvisions.boot
  web:
    admin-ui:
      url: http://dashboard.cdc.iocoder.cn # Admin 管理后台 UI 的地址
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  security:
    permit-all_urls:
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
      - /admin-api/dispatch/freezer/**
      - /admin-api/dispatch/ydg/**
      - /admin-api/dispatch/agv/**
      - /admin-api/dispatch/reply/**
      - /admin-api/dispatch/external/**
      - /admin-api/dispatch/deliveryWindow/**
      - /admin-api/dispatch/alarm/**
      - /admin-api/dispatch/**
  websocket:
    enable: true # websocket的开关
    path: /infra/ws # 路径
    sender-type: local # 消息发送的类型，可选值为 local、redis、rocketmq、kafka、rabbitmq
    sender-rocketmq:
      topic: ${spring.application.name}-websocket # 消息发送的 RocketMQ Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 RocketMQ Consumer Group
    sender-rabbitmq:
      exchange: ${spring.application.name}-websocket-exchange # 消息发送的 RabbitMQ Exchange
      queue: ${spring.application.name}-websocket-queue # 消息发送的 RabbitMQ Queue
    sender-kafka:
      topic: ${spring.application.name}-websocket # 消息发送的 Kafka Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 Kafka Consumer Group
  swagger:
    title: 开发平台
    description: api接口文档
    version: ${cdc.info.version}
    url: ${cdc.web.admin-ui.url}
    email:
    license: MIT
    license-url:
  codegen:
    base-package: ${cdc.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 20 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/system/tenant/get-id-by-name # 基于名字获取租户，不许带租户编号
      - /admin-api/system/tenant/get-by-website # 基于域名获取租户，不许带租户编号
      - /admin-api/system/captcha/get # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/check # 校验图片验证码，和租户无关
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上租户编号
      - /admin-api/pay/notify/** # 支付回调通知，不携带租户编号
      - /jmreport/* # 积木报表，无法携带租户编号
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，无法携带租户编号
    ignore-tables:
      - system_tenant
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_client
      - system_mail_account
      - system_mail_template
      - system_mail_log
      - system_notify_template
      - infra_codegen_column
      - infra_codegen_table
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_job_log
      - infra_data_source_config
      - jimu_dict
      - jimu_dict_item
      - jimu_report
      - jimu_report_data_source
      - jimu_report_db
      - jimu_report_db_field
      - jimu_report_db_param
      - jimu_report_link
      - jimu_report_map
      - jimu_report_share
      - rep_demo_dxtj
      - rep_demo_employee
      - rep_demo_gongsi
      - rep_demo_jianpiao
      - tmp_report_data_1
      - tmp_report_data_income
    ignore-caches:
      - user_role_ids
      - permission_menu_ids
      - oauth_client
      - notify_template
      - mail_account
      - mail_template
      - sms_template
  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1m
    send-maximum-quantity-per-day: 10
    begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
    end-code: 9999 # 这里配置 9999 的原因是，测试方便。
  trade:
    order:
      pay-expire-time: 2h # 支付的过期时间
      receive-expire-time: 14d # 收货的过期时间
      comment-expire-time: 7d # 评论的过期时间
    express:
      client: kd_niao
      kd-niao:
        api-key: cb022f1e-48f1-4c4a-a723-9001ac9676b8
        business-id: 1809751
      kd100:
        key: pLXUGAwK5305
        customer: E77DF18BE109F454A5CD319E44BF5177

logging:
  level:
    tracer: trace # 开启trace级别日志,在开发时可以开启此配置,则控制台可以打印es全部请求信息及DSL语句,为了避免重复,开启此项配置后,可以将EE的print-dsl设置为false

--- #################### iot相关配置 ####################
iot:
  emq:
    emqCode: CD
    # 主机地址
    hostUrl: *************:9000
    backUrl: http://*************:9220

debug: false



liteflow:
  #规则文件路径
  rule-source: config/flow_el_new.xml
  #-----------------以下非必须-----------------
  #liteflow是否开启，默认为true
  enable: true
  #liteflow的banner打印是否开启，默认为true
  print-banner: true
  #上下文的初始数量槽，默认值为1024，这个值不用刻意配置，这个值会自动扩容
  slot-size: 1024
  #FlowExecutor的execute2Future的线程数，默认为64
  main-executor-works: 64
  #FlowExecutor的execute2Future的自定义线程池Builder，LiteFlow提供了默认的Builder
  main-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultMainExecutorBuilder
  #自定义请求ID的生成类，LiteFlow提供了默认的生成类
  request-id-generator-class: com.yomahub.liteflow.flow.id.DefaultRequestIdGenerator
  #全局异步节点线程池大小，默认为64
  global-thread-pool-size: 64
  #全局异步节点线程池队列大小，默认为512
  global-thread-pool-queue-size: 512
  #全局异步节点线程池自定义Builder，LiteFlow提供了默认的线程池Builder
  global-thread-pool-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultGlobalExecutorBuilder
  #异步线程最长的等待时间(只用于when)，默认值为15000
  when-max-wait-time: 15
  #异步线程最长的等待时间(只用于when)，默认值为MILLISECONDS，毫秒
  when-max-wait-time-unit: MINUTES
  #每个WHEN是否用单独的线程池
  when-thread-pool-isolate: false
  #设置解析模式，一共有三种模式，PARSE_ALL_ON_START | PARSE_ALL_ON_FIRST_EXEC | PARSE_ONE_ON_FIRST_EXEC
  parse-mode: PARSE_ALL_ON_START
  #全局重试次数，默认为0
  retry-count: 0
  #是否支持不同类型的加载方式混用，默认为false
  support-multiple-type: false
  #全局默认节点执行器
  node-executor-class: com.yomahub.liteflow.flow.executor.DefaultNodeExecutor
  #是否打印执行中过程中的日志，默认为true
  print-execution-log: true
  #是否开启本地文件监听，默认为false
  enable-monitor-file: true
  #是否开启快速解析模式，默认为false
  fast-load: false
  #是否开启Node节点实例ID持久化，默认为false
  enable-node-instance-id: false
  #简易监控配置选项
  monitor:
    #监控是否开启，默认不开启
    enable-log: false
    #监控队列存储大小，默认值为200
    queue-limit: 200
    #监控一开始延迟多少执行，默认值为300000毫秒，也就是5分钟
    delay: 300000
    #监控日志打印每过多少时间执行一次，默认值为300000毫秒，也就是5分钟
    period: 300000

device:
  types:
    - type: 1
      station: station2
      deviceCode: GigaUlt003
      url: ************:8081
      deviceName: 大库1号
      deviceDesc: 超低温自动化一号库
      temperature: -80.0
    - type: 1
      station: station3
      deviceCode: GigaUlt004
      url: 192.168.1.43:8081
      deviceName: 大库2号
      deviceDesc: 超低温自动化二号库
      temperature: -80.0
    - type: 2
      station: station4
      deviceCode: HCryo240126001
      url: 192.168.1.62:8086
      deviceName: 液氮罐1号
      deviceDesc: 深低温自动化一号库
      temperature: -180.0
    - type: 2
      station: station5
      deviceCode: HCryo240126002
      url: 192.168.1.72:8086
      deviceName: 液氮罐2号
      deviceDesc: 深低温自动化二号库
      temperature: -180.0
