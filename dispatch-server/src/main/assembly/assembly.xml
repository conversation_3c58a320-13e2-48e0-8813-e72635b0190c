<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.0 http://maven.apache.org/xsd/assembly-2.1.0.xsd">
    <id>package</id>
    <formats>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <baseDirectory>dispatch-api-${project.version}</baseDirectory>

    <fileSets>
        <!-- 添加启动脚本 -->
        <fileSet>
            <directory>src/main/scripts</directory>
            <outputDirectory>bin</outputDirectory>
            <includes>
                <include>*.sh</include>
                <include>*.bat</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>

        <!-- 添加配置文件 -->
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory>config</outputDirectory>
            <includes>
                <include>application*.yml</include>
                <include>application*.yaml</include>
                <include>application*.properties</include>
                <include>*.xml</include>
            </includes>
            <fileMode>0644</fileMode>
        </fileSet>

        <!-- 创建日志目录 -->
        <fileSet>
            <directory>src/main/assembly</directory>
            <outputDirectory>logs</outputDirectory>
            <excludes>
                <exclude>**/*</exclude>
            </excludes>
            <directoryMode>0755</directoryMode>
        </fileSet>

        <!-- 添加文档 -->
        <fileSet>
            <directory>${project.parent.basedir}</directory>
            <outputDirectory></outputDirectory>
            <includes>
                <include>README.md</include>
            </includes>
            <fileMode>0644</fileMode>
        </fileSet>
    </fileSets>

    <!-- 只添加主JAR文件，由Spring Boot Maven插件生成的可执行JAR -->
    <files>
        <file>
            <source>${project.build.directory}/${project.build.finalName}.jar</source>
            <outputDirectory>lib</outputDirectory>
            <destName>dispatch-server.jar</destName>
        </file>
    </files>
</assembly> 