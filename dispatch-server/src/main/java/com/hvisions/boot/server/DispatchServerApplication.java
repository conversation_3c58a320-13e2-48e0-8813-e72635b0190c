package com.hvisions.boot.server;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${cdc.info.base-package}
@EntityScan(basePackages = "${cdc.info.base-package}.module.*.dal")
//@EsMapperScan(value = "com.hvisions.boot.module.*.es.mapper")
//@EnableRetry
@EnableAsync
@EnableScheduling // 启用调度
@SpringBootApplication(scanBasePackages = {"${cdc.info.base-package}.server", "${cdc.info.base-package}.module"})
//@ComponentScan(basePackages = {"com.hvisions.boot", "net.somta.juggle"})
public class DispatchServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(DispatchServerApplication.class, args);
        /*SpringApplication app = new SpringApplication(CdcServerApplication.class);
        ApplicationContext ctx = app.run(args);
        Environment environment = ctx.getEnvironment();

        Boolean maintenanceMode = environment.getProperty("maintenance-mode", Boolean.class);
        if (Boolean.FALSE.equals(maintenanceMode)) {
            TaskThread tt = new TaskThread();
            tt.addObserver(new TaskListener());
            Thread thread = new Thread(tt);
            thread.setName("TaskThread");
            thread.start();
            log.info("TaskThread启动");
        }*/
    }

}
