@echo off
setlocal enabledelayedexpansion

:: 配置信息
set APP_NAME=dispatch-api
set JAR_NAME=dispatch-server.jar
set JAVA_HOME=C:\Program Files\Java\jdk-17.0.14
set JAVA_BIN=%JAVA_HOME%\bin\java.exe
set JAVA_OPTS=--add-opens java.base/sun.reflect.annotation=ALL-UNNAMED -Xmx1024M -Xms256M

:: 获取程序根目录
cd /d %~dp0
cd ..
set APP_HOME=%cd%

:: 创建PID文件和日志目录
set PID_FILE=%APP_HOME%\logs\%APP_NAME%.pid
set LOG_DIR=%APP_HOME%\logs
set APP_CONFIG_FILE=%APP_HOME%\config\application.yaml

:: 从application.yaml中获取spring.application.name作为日志文件名
set SPRING_APP_NAME=dispatch-server
if exist "%APP_CONFIG_FILE%" (
    for /f "tokens=2 delims=: " %%a in ('findstr /c:"name:" "%APP_CONFIG_FILE%"') do (
        set SPRING_APP_NAME=%%a
    )
)
set LOG_FILE=%LOG_DIR%\%SPRING_APP_NAME%.log

:: 检查Java是否正确安装
if not exist "%JAVA_BIN%" (
    echo 错误: Java可执行文件未找到: %JAVA_BIN%
    exit /b 1
)

:: 检查JAR文件
set JAR_PATH=%APP_HOME%\lib\%JAR_NAME%
if not exist "%JAR_PATH%" (
    echo 错误: 找不到JAR文件: %JAR_PATH%
    exit /b 1
)

:: 主逻辑
if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="status" goto status
if "%1"=="logs" goto logs
if "%1"=="tail" goto tail_logs
if "%1"=="search" goto search_logs
goto usage

:status
if exist "%PID_FILE%" (
    set /p PID=<"%PID_FILE%"
    tasklist /fi "PID eq !PID!" 2>nul | find "!PID!" >nul
    if !errorlevel! equ 0 (
        echo %APP_NAME% 正在运行，进程ID: !PID!
        exit /b 0
    ) else (
        echo %APP_NAME% 已停止，但PID文件仍存在
        del "%PID_FILE%" 2>nul
        exit /b 1
    )
) else (
    echo %APP_NAME% 未运行
    exit /b 1
)

:start
echo 正在启动 %APP_NAME%...
call :status >nul 2>&1
if !errorlevel! equ 0 (
    echo %APP_NAME% 已经在运行
    exit /b 0
)

:: 创建日志目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

:: 拼接启动命令
set APP_OPTS=-Dspring.config.location=file:%APP_HOME%\config\ -Dcdc.info.base-package=com.hvisions.boot
set APP_OPTS=%APP_OPTS% -DLOG_FILE=%LOG_FILE%

:: 启动应用
start /b "" "%JAVA_BIN%" %JAVA_OPTS% %APP_OPTS% -jar "%JAR_PATH%" >nul 2>&1

:: 获取进程ID并保存
timeout /t 2 >nul
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "%JAR_NAME%"') do (
    set PID=%%a
    set PID=!PID:"=!
)

if defined PID (
    echo !PID! > "%PID_FILE%"
    echo %APP_NAME% 启动成功，进程ID: !PID!
    echo 日志文件: %LOG_FILE%
) else (
    echo %APP_NAME% 启动失败，请检查日志文件: %LOG_FILE%
    exit /b 1
)
exit /b 0

:stop
echo 正在停止 %APP_NAME%...
if not exist "%PID_FILE%" (
    echo %APP_NAME% 未运行
    exit /b 0
)

set /p PID=<"%PID_FILE%"
tasklist /fi "PID eq %PID%" 2>nul | find "%PID%" >nul
if !errorlevel! equ 0 (
    echo 正在停止进程 %PID%...
    taskkill /pid %PID% /f >nul 2>&1
    timeout /t 2 >nul
    
    tasklist /fi "PID eq %PID%" 2>nul | find "%PID%" >nul
    if !errorlevel! equ 0 (
        echo 无法停止 %APP_NAME%
        exit /b 1
    ) else (
        echo %APP_NAME% 已停止
        del "%PID_FILE%" 2>nul
    )
) else (
    echo %APP_NAME% 已停止，但PID文件仍存在
    del "%PID_FILE%" 2>nul
)
exit /b 0

:restart
call :stop
timeout /t 2 >nul
call :start
exit /b 0

:logs
set LINES=%2
if "%LINES%"=="" set LINES=100
if exist "%LOG_FILE%" (
    powershell "Get-Content '%LOG_FILE%' -Tail %LINES%"
) else (
    echo 日志文件不存在: %LOG_FILE%
    exit /b 1
)
exit /b 0

:tail_logs
if exist "%LOG_FILE%" (
    echo 正在实时查看日志，按 Ctrl+C 退出...
    powershell "Get-Content '%LOG_FILE%' -Wait -Tail 10"
) else (
    echo 日志文件不存在: %LOG_FILE%
    exit /b 1
)
exit /b 0

:search_logs
if "%2"=="" (
    echo 请提供搜索关键词
    exit /b 1
)
if exist "%LOG_FILE%" (
    echo 搜索日志中包含 '%2' 的内容：
    findstr /i "%2" "%LOG_FILE%"
) else (
    echo 日志文件不存在: %LOG_FILE%
    exit /b 1
)
exit /b 0

:usage
echo 用法: %0 {start^|stop^|restart^|status^|logs [行数]^|tail^|search [关键词]}
exit /b 1