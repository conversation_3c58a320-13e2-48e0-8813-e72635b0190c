@echo off
setlocal enabledelayedexpansion

:: 配置信息
set APP_NAME=dispatch-api
set JAR_NAME=dispatch-server.jar
set JAVA_OPTS=--add-opens java.base/sun.reflect.annotation=ALL-UNNAMED -Xmx1024M -Xms256M

:: 获取程序根目录
cd /d %~dp0
cd ..
set APP_HOME=%cd%

:: 创建PID文件和日志目录
set PID_FILE=%APP_HOME%\logs\%APP_NAME%.pid
set LOG_DIR=%APP_HOME%\logs
set APP_CONFIG_FILE=%APP_HOME%\config\application.yaml

:: 从application.yaml中获取spring.application.name作为日志文件名
set SPRING_APP_NAME=dispatch-server
if exist "%APP_CONFIG_FILE%" (
    for /f "tokens=2 delims=: " %%a in ('findstr /c:"name:" "%APP_CONFIG_FILE%" 2^>nul') do (
        set SPRING_APP_NAME=%%a
        set SPRING_APP_NAME=!SPRING_APP_NAME: =!
    )
)
set LOG_FILE=%LOG_DIR%\%SPRING_APP_NAME%.log

:: 自动检测Java路径
set JAVA_BIN=java
if defined JAVA_HOME (
    set JAVA_BIN=%JAVA_HOME%\bin\java.exe
    if not exist "!JAVA_BIN!" (
        set JAVA_BIN=%JAVA_HOME%\bin\java
    )
)

:: 检查Java是否可用
"%JAVA_BIN%" -version >nul 2>&1
if !errorlevel! neq 0 (
    echo 错误: Java未正确安装或未配置环境变量
    echo 请确保Java已安装并配置了JAVA_HOME环境变量
    pause
    exit /b 1
)

:: 检查JAR文件
set JAR_PATH=%APP_HOME%\lib\%JAR_NAME%
if not exist "%JAR_PATH%" (
    echo 错误: 找不到JAR文件: %JAR_PATH%
    echo 当前目录: %APP_HOME%
    echo 请确保应用已正确部署
    pause
    exit /b 1
)

:: 主逻辑
:: 如果没有参数（双击运行），默认执行start
if "%1"=="" (
    echo 检测到双击运行，将启动服务...
    echo.
    goto start
)

if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="status" goto status
if "%1"=="logs" goto logs
if "%1"=="tail" goto tail_logs
if "%1"=="search" goto search_logs
goto usage

:status
if exist "%PID_FILE%" (
    set /p PID=<"%PID_FILE%"
    tasklist /fi "PID eq !PID!" 2>nul | find "!PID!" >nul
    if !errorlevel! equ 0 (
        echo %APP_NAME% 正在运行，进程ID: !PID!
        echo 日志文件: %LOG_FILE%
        if "%1"=="status" pause
        exit /b 0
    ) else (
        echo %APP_NAME% 已停止，清理PID文件
        del "%PID_FILE%" 2>nul
        if "%1"=="status" pause
        exit /b 1
    )
) else (
    echo %APP_NAME% 未运行
    if "%1"=="status" pause
    exit /b 1
)

:start
echo 正在启动 %APP_NAME%...
call :status >nul 2>&1
if !errorlevel! equ 0 (
    echo %APP_NAME% 已经在运行
    if "%1"=="start" pause
    exit /b 0
)

:: 创建日志目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

:: 拼接启动命令
set APP_OPTS=-Dspring.config.location=file:%APP_HOME%\config\ -Dcdc.info.base-package=com.hvisions.boot
set APP_OPTS=%APP_OPTS% -DLOG_FILE=%LOG_FILE%

echo 启动命令: "%JAVA_BIN%" %JAVA_OPTS% %APP_OPTS% -jar "%JAR_PATH%"
echo 配置目录: %APP_HOME%\config\
echo 日志文件: %LOG_FILE%
echo.

:: 启动应用 - 使用wmic获取准确的进程ID
echo 正在启动Java进程...
wmic process call create '"%JAVA_BIN%" %JAVA_OPTS% %APP_OPTS% -jar "%JAR_PATH%"' | findstr "ProcessId" > "%TEMP%\pid.tmp"
if exist "%TEMP%\pid.tmp" (
    for /f "tokens=3 delims=; " %%a in (%TEMP%\pid.tmp) do (
        set PID=%%a
        set PID=!PID: =!
    )
    del "%TEMP%\pid.tmp" 2>nul
)

:: 等待进程启动
echo 等待应用启动...
timeout /t 5 >nul

:: 验证进程是否存在
if defined PID (
    tasklist /fi "PID eq !PID!" 2>nul | find "!PID!" >nul
    if !errorlevel! equ 0 (
        echo !PID! > "%PID_FILE%"
        echo %APP_NAME% 启动成功，进程ID: !PID!
        echo 日志文件: %LOG_FILE%
        echo.
        echo 可以使用以下命令管理服务:
        echo   %~nx0 status  - 查看状态
        echo   %~nx0 stop    - 停止服务
        echo   %~nx0 logs    - 查看日志
        echo   %~nx0 tail    - 实时查看日志
    ) else (
        echo %APP_NAME% 启动失败，进程已退出
        if exist "%LOG_FILE%" (
            echo 最近的错误日志:
            powershell "Get-Content '%LOG_FILE%' -Tail 20"
        )
        if "%1"=="start" pause
        exit /b 1
    )
) else (
    echo %APP_NAME% 启动失败，无法获取进程ID
    if exist "%LOG_FILE%" (
        echo 最近的错误日志:
        powershell "Get-Content '%LOG_FILE%' -Tail 20"
    )
    if "%1"=="start" pause
    exit /b 1
)

if "%1"=="start" pause
exit /b 0

:stop
echo 正在停止 %APP_NAME%...
if not exist "%PID_FILE%" (
    echo %APP_NAME% 未运行
    if "%1"=="stop" pause
    exit /b 0
)

set /p PID=<"%PID_FILE%"
tasklist /fi "PID eq %PID%" 2>nul | find "%PID%" >nul
if !errorlevel! equ 0 (
    echo 正在停止进程 %PID%...
    taskkill /pid %PID% /f >nul 2>&1
    timeout /t 3 >nul

    tasklist /fi "PID eq %PID%" 2>nul | find "%PID%" >nul
    if !errorlevel! equ 0 (
        echo 警告: 无法正常停止 %APP_NAME%，尝试强制终止...
        taskkill /pid %PID% /f /t >nul 2>&1
        timeout /t 2 >nul
        tasklist /fi "PID eq %PID%" 2>nul | find "%PID%" >nul
        if !errorlevel! equ 0 (
            echo 错误: 无法停止 %APP_NAME%
            if "%1"=="stop" pause
            exit /b 1
        )
    )
    echo %APP_NAME% 已停止
    del "%PID_FILE%" 2>nul
) else (
    echo %APP_NAME% 已停止，清理PID文件
    del "%PID_FILE%" 2>nul
)

if "%1"=="stop" pause
exit /b 0

:restart
call :stop
timeout /t 2 >nul
call :start
exit /b 0

:logs
set LINES=%2
if "%LINES%"=="" set LINES=100
if exist "%LOG_FILE%" (
    powershell "Get-Content '%LOG_FILE%' -Tail %LINES%"
) else (
    echo 日志文件不存在: %LOG_FILE%
    exit /b 1
)
exit /b 0

:tail_logs
if exist "%LOG_FILE%" (
    echo 正在实时查看日志，按 Ctrl+C 退出...
    powershell "Get-Content '%LOG_FILE%' -Wait -Tail 10"
) else (
    echo 日志文件不存在: %LOG_FILE%
    exit /b 1
)
exit /b 0

:search_logs
if "%2"=="" (
    echo 请提供搜索关键词
    exit /b 1
)
if exist "%LOG_FILE%" (
    echo 搜索日志中包含 '%2' 的内容：
    findstr /i "%2" "%LOG_FILE%"
) else (
    echo 日志文件不存在: %LOG_FILE%
    exit /b 1
)
exit /b 0

:usage
echo.
echo ===============================================
echo           %APP_NAME% 服务管理脚本
echo ===============================================
echo.
echo 用法: %~nx0 [命令] [参数]
echo.
echo 可用命令:
echo   start                启动服务
echo   stop                 停止服务
echo   restart              重启服务
echo   status               查看服务状态
echo   logs [行数]          查看日志 (默认100行)
echo   tail                 实时查看日志
echo   search [关键词]      搜索日志内容
echo.
echo 示例:
echo   %~nx0 start          启动服务
echo   %~nx0 logs 50        查看最近50行日志
echo   %~nx0 search ERROR   搜索包含ERROR的日志
echo.
echo 注意: 双击运行时会自动执行start命令
echo.
pause
exit /b 1