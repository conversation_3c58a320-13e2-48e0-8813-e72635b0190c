#!/bin/bash

# 配置信息
APP_NAME="dispatch-api"
JAR_NAME="dispatch-server.jar"
JAVA_HOME="/home/<USER>/java/jdk-17.0.14"
JAVA_BIN="$JAVA_HOME/bin/java"
JAVA_OPTS="--add-opens java.base/sun.reflect.annotation=ALL-UNNAMED -Xmx1024M -Xms256M"

# 获取程序根目录
cd `dirname $0`
cd ..
APP_HOME=`pwd`

# 创建PID文件和日志目录
PID_FILE="$APP_HOME/logs/$APP_NAME.pid"
LOG_DIR="$APP_HOME/logs"
# 从application.yaml中获取spring.application.name作为日志文件名
APP_CONFIG_FILE="$APP_HOME/config/application.yaml"
if [ -f "$APP_CONFIG_FILE" ]; then
    SPRING_APP_NAME=$(grep -A1 "spring:" "$APP_CONFIG_FILE" | grep "name:" | awk -F': ' '{print $2}' | tr -d ' ')
fi
# 如果获取失败，使用默认值
if [ -z "$SPRING_APP_NAME" ]; then
    SPRING_APP_NAME="dispatch-server"
fi
LOG_FILE="$LOG_DIR/$SPRING_APP_NAME.log"

# 检查Java是否正确安装
if [ ! -x "$JAVA_BIN" ]; then
    echo "错误: Java可执行文件未找到: $JAVA_BIN"
    exit 1
fi

# 检查JAR文件
JAR_PATH="$APP_HOME/lib/$JAR_NAME"
if [ ! -f "$JAR_PATH" ]; then
    echo "错误: 找不到JAR文件: $JAR_PATH"
    exit 1
fi

# 获取进程状态
status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null; then
            echo "$APP_NAME 正在运行，进程ID: $PID"
            return 0
        else
            echo "$APP_NAME 已停止，但PID文件仍存在"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        echo "$APP_NAME 未运行"
        return 1
    fi
}

# 启动应用
start() {
    echo "正在启动 $APP_NAME..."
    if status > /dev/null; then
        echo "$APP_NAME 已经在运行"
        return 0
    fi
    
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    # 拼接启动命令
    # 指定配置文件路径和基础包名
    APP_OPTS="-Dspring.config.location=file:$APP_HOME/config/ -Dcdc.info.base-package=com.hvisions.boot"
    
    # 添加日志文件参数，确保logback使用正确的日志文件路径
    APP_OPTS="$APP_OPTS -DLOG_FILE=$LOG_FILE"
    
    nohup $JAVA_BIN $JAVA_OPTS $APP_OPTS -jar $JAR_PATH > /dev/null 2>&1 &
    PID=$!
    echo $PID > "$PID_FILE"
    sleep 2
    
    if ps -p $PID > /dev/null; then
        echo "$APP_NAME 启动成功，进程ID: $PID"
        echo "日志文件: $LOG_FILE"
    else
        echo "$APP_NAME 启动失败，请检查日志文件: $LOG_FILE"
        return 1
    fi
}

# 停止应用
stop() {
    echo "正在停止 $APP_NAME..."
    if [ ! -f "$PID_FILE" ]; then
        echo "$APP_NAME 未运行"
        return 0
    fi
    
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "正在停止进程 $PID..."
        kill $PID
        
        # 等待进程结束
        TIMEOUT=30
        while ps -p $PID > /dev/null && [ $TIMEOUT -gt 0 ]; do
            sleep 1
            TIMEOUT=$((TIMEOUT-1))
        done
        
        if ps -p $PID > /dev/null; then
            echo "进程未能正常停止，尝试强制终止..."
            kill -9 $PID
            sleep 2
        fi
        
        if ps -p $PID > /dev/null; then
            echo "无法停止 $APP_NAME"
            return 1
        else
            echo "$APP_NAME 已停止"
            rm -f "$PID_FILE"
        fi
    else
        echo "$APP_NAME 已停止，但PID文件仍存在"
        rm -f "$PID_FILE"
    fi
}

# 重启应用
restart() {
    stop
    sleep 2
    start
}

# 查看日志
logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -n ${1:-100} "$LOG_FILE"
    else
        echo "日志文件不存在: $LOG_FILE"
        return 1
    fi
}

# 实时查看日志
tail_logs() {
    if [ -f "$LOG_FILE" ]; then
        echo "正在实时查看日志，按 Ctrl+C 退出..."
        tail -f "$LOG_FILE"
    else
        echo "日志文件不存在: $LOG_FILE"
        return 1
    fi
}

# 搜索日志内容
search_logs() {
    if [ -z "$1" ]; then
        echo "请提供搜索关键词"
        return 1
    fi
    
    if [ -f "$LOG_FILE" ]; then
        echo "搜索日志中包含 '$1' 的内容："
        grep -i "$1" "$LOG_FILE"
    else
        echo "日志文件不存在: $LOG_FILE"
        return 1
    fi
}

# 脚本使用说明
usage() {
    echo "用法: $0 {start|stop|restart|status|logs [行数]|tail|search [关键词]}"
    exit 1
}

# 主逻辑
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs $2
        ;;
    tail)
        tail_logs
        ;;
    search)
        search_logs "$2"
        ;;
    *)
        usage
        ;;
esac

exit 0 