@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 设置窗口标题
title Dispatch API 服务启动器

echo.
echo ===============================================
echo           Dispatch API 服务启动器
echo ===============================================
echo.

:: 配置信息
set APP_NAME=dispatch-api
set JAR_NAME=dispatch-server.jar

:: 获取程序根目录
cd /d %~dp0
cd ..
set APP_HOME=%cd%

echo 应用目录: %APP_HOME%
echo.

:: 检查必要文件
set JAR_PATH=%APP_HOME%\lib\%JAR_NAME%
if not exist "%JAR_PATH%" (
    echo [错误] 找不到JAR文件: %JAR_PATH%
    echo.
    echo 请确保应用已正确部署，目录结构应为:
    echo   %APP_HOME%\
    echo   ├── bin\
    echo   ├── lib\%JAR_NAME%
    echo   ├── config\
    echo   └── logs\
    echo.
    pause
    exit /b 1
)

:: 检查配置目录
if not exist "%APP_HOME%\config" (
    echo [错误] 找不到配置目录: %APP_HOME%\config
    echo.
    pause
    exit /b 1
)

:: 自动检测Java
set JAVA_BIN=java
if defined JAVA_HOME (
    if exist "%JAVA_HOME%\bin\java.exe" (
        set JAVA_BIN=%JAVA_HOME%\bin\java.exe
    ) else if exist "%JAVA_HOME%\bin\java" (
        set JAVA_BIN=%JAVA_HOME%\bin\java
    )
)

:: 检查Java是否可用
echo 检查Java环境...
"%JAVA_BIN%" -version >nul 2>&1
if !errorlevel! neq 0 (
    echo [错误] Java未正确安装或未配置环境变量
    echo.
    echo 请确保:
    echo 1. Java 17或更高版本已安装
    echo 2. JAVA_HOME环境变量已配置
    echo 3. 或者java命令在PATH中可用
    echo.
    pause
    exit /b 1
)

:: 显示Java版本
echo Java环境检查通过
"%JAVA_BIN%" -version 2>&1 | findstr "version"
echo.

:: 检查服务是否已运行
set PID_FILE=%APP_HOME%\logs\%APP_NAME%.pid
if exist "%PID_FILE%" (
    set /p PID=<"%PID_FILE%"
    tasklist /fi "PID eq !PID!" 2>nul | find "!PID!" >nul
    if !errorlevel! equ 0 (
        echo [警告] 服务已在运行，进程ID: !PID!
        echo.
        choice /c YN /m "是否重启服务? (Y/N)"
        if !errorlevel! equ 1 (
            echo 正在停止现有服务...
            taskkill /pid !PID! /f >nul 2>&1
            timeout /t 3 >nul
            del "%PID_FILE%" 2>nul
        ) else (
            echo 取消启动
            pause
            exit /b 0
        )
    ) else (
        del "%PID_FILE%" 2>nul
    )
)

:: 创建日志目录
if not exist "%APP_HOME%\logs" mkdir "%APP_HOME%\logs"

:: 启动服务
echo 正在启动 %APP_NAME% 服务...
echo.

set JAVA_OPTS=--add-opens java.base/sun.reflect.annotation=ALL-UNNAMED -Xmx1024M -Xms256M
set APP_OPTS=-Dspring.config.location=file:%APP_HOME%\config\ -Dcdc.info.base-package=com.hvisions.boot
set LOG_FILE=%APP_HOME%\logs\dispatch-server.log
set APP_OPTS=%APP_OPTS% -DLOG_FILE=%LOG_FILE%

echo 启动命令: "%JAVA_BIN%" %JAVA_OPTS% %APP_OPTS% -jar "%JAR_PATH%"
echo 配置目录: %APP_HOME%\config\
echo 日志文件: %LOG_FILE%
echo.

:: 使用wmic启动进程并获取PID
wmic process call create '"%JAVA_BIN%" %JAVA_OPTS% %APP_OPTS% -jar "%JAR_PATH%"' | findstr "ProcessId" > "%TEMP%\pid.tmp"
if exist "%TEMP%\pid.tmp" (
    for /f "tokens=3 delims=; " %%a in (%TEMP%\pid.tmp) do (
        set PID=%%a
        set PID=!PID: =!
    )
    del "%TEMP%\pid.tmp" 2>nul
)

if defined PID (
    echo 服务启动中，进程ID: !PID!
    echo !PID! > "%PID_FILE%"
    echo.
    echo 等待服务启动完成...
    
    :: 等待服务启动
    set /a count=0
    :wait_loop
    timeout /t 2 >nul
    set /a count+=1
    
    :: 检查进程是否还存在
    tasklist /fi "PID eq !PID!" 2>nul | find "!PID!" >nul
    if !errorlevel! neq 0 (
        echo [错误] 服务启动失败，进程已退出
        goto show_error_log
    )
    
    :: 检查是否启动成功（检查日志或端口）
    if exist "%LOG_FILE%" (
        findstr /c:"Started" "%LOG_FILE%" >nul 2>&1
        if !errorlevel! equ 0 (
            echo.
            echo ===============================================
            echo           服务启动成功！
            echo ===============================================
            echo 进程ID: !PID!
            echo 日志文件: %LOG_FILE%
            echo.
            echo 可以使用以下命令管理服务:
            echo   bin\dispatch-server.bat status  - 查看状态
            echo   bin\dispatch-server.bat stop    - 停止服务
            echo   bin\dispatch-server.bat logs    - 查看日志
            echo.
            pause
            exit /b 0
        )
    )
    
    if !count! lss 30 goto wait_loop
    
    echo [警告] 服务可能仍在启动中，请稍后检查日志
    echo 日志文件: %LOG_FILE%
    echo.
    pause
    exit /b 0
) else (
    echo [错误] 无法启动服务，获取进程ID失败
    goto show_error_log
)

:show_error_log
echo.
echo 启动失败，查看错误日志:
if exist "%LOG_FILE%" (
    echo ===============================================
    powershell "Get-Content '%LOG_FILE%' -Tail 20"
    echo ===============================================
) else (
    echo 日志文件不存在: %LOG_FILE%
)
echo.
pause
exit /b 1
