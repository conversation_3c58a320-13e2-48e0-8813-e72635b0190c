# Dispatch API Windows 启动脚本使用说明

## 脚本文件说明

### 主要脚本
- `start.bat` - 简单启动脚本，支持双击运行
- `stop.bat` - 停止服务脚本
- `status.bat` - 查看服务状态脚本
- `dispatch-server.bat` - 完整的服务管理脚本

### 使用方法

#### 1. 双击启动（推荐）
直接双击 `start.bat` 文件即可启动服务。脚本会：
- 自动检测Java环境
- 检查必要文件是否存在
- 启动服务并显示详细信息
- 等待服务启动完成

#### 2. 命令行使用
```batch
# 启动服务
bin\start.bat

# 停止服务
bin\stop.bat

# 查看状态
bin\status.bat

# 使用完整管理脚本
bin\dispatch-server.bat start
bin\dispatch-server.bat stop
bin\dispatch-server.bat status
bin\dispatch-server.bat logs
bin\dispatch-server.bat tail
```

## 部署目录结构

确保部署后的目录结构如下：
```
dispatch-api-x.x.x/
├── bin/
│   ├── start.bat              # 简单启动脚本
│   ├── stop.bat               # 停止脚本
│   ├── status.bat             # 状态检查脚本
│   └── dispatch-server.bat    # 完整管理脚本
├── lib/
│   └── dispatch-server.jar    # 应用JAR文件
├── config/
│   ├── application.yaml       # 主配置文件
│   ├── application-prod.yaml  # 生产环境配置
│   └── logback-spring.xml     # 日志配置
├── logs/                      # 日志目录（自动创建）
└── README.md
```

## 环境要求

### Java环境
- Java 17 或更高版本
- 配置 `JAVA_HOME` 环境变量（推荐）
- 或确保 `java` 命令在 PATH 中可用

### 系统要求
- Windows 7 或更高版本
- 支持中文字符显示

## 常见问题

### 1. Java环境问题
**错误**: `Java未正确安装或未配置环境变量`

**解决方案**:
- 确保Java 17+已安装
- 配置JAVA_HOME环境变量指向Java安装目录
- 或将Java的bin目录添加到PATH环境变量

### 2. 文件找不到
**错误**: `找不到JAR文件`

**解决方案**:
- 确保目录结构正确
- 检查lib目录下是否有dispatch-server.jar文件
- 确保在正确的目录下运行脚本

### 3. 端口占用
**错误**: 服务启动失败，端口被占用

**解决方案**:
- 检查配置文件中的端口设置
- 使用 `netstat -ano | findstr :端口号` 查看端口占用
- 停止占用端口的进程或修改配置文件中的端口

### 4. 权限问题
**错误**: 无法创建文件或目录

**解决方案**:
- 以管理员身份运行脚本
- 确保对应用目录有读写权限

## 日志查看

### 查看实时日志
```batch
bin\dispatch-server.bat tail
```

### 查看最近日志
```batch
bin\dispatch-server.bat logs 100
```

### 搜索日志
```batch
bin\dispatch-server.bat search ERROR
```

## 服务管理

### 开机自启动
1. 将start.bat创建快捷方式
2. 将快捷方式放入Windows启动文件夹：
   - 按 Win+R，输入 `shell:startup`
   - 将快捷方式复制到打开的文件夹中

### 作为Windows服务运行
推荐使用NSSM (Non-Sucking Service Manager)：
1. 下载NSSM
2. 使用命令安装服务：
   ```batch
   nssm install DispatchAPI "C:\path\to\start.bat"
   ```

## 故障排除

1. **检查日志文件**: logs/dispatch-server.log
2. **检查配置文件**: config/application.yaml
3. **验证Java版本**: `java -version`
4. **检查端口占用**: `netstat -ano | findstr :8080`
5. **查看进程状态**: 使用status.bat脚本

## 联系支持

如果遇到问题，请提供以下信息：
- 错误信息截图
- 日志文件内容
- Java版本信息
- 系统版本信息



========================面板账户登录信息==========================

【云服务器】请在安全组放行 39355 端口
外网ipv4面板地址: https://*************:39355/24c3f3d2
内网面板地址:     https://***********:39355/24c3f3d2
username: dauk2o4i
password: 253468d8

浏览器访问以下链接，添加宝塔客服
