@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 设置窗口标题
title Dispatch API 服务状态

echo.
echo ===============================================
echo           Dispatch API 服务状态
echo ===============================================
echo.

:: 配置信息
set APP_NAME=dispatch-api

:: 获取程序根目录
cd /d %~dp0
cd ..
set APP_HOME=%cd%

echo 应用目录: %APP_HOME%
echo.

:: 检查PID文件
set PID_FILE=%APP_HOME%\logs\%APP_NAME%.pid
if not exist "%PID_FILE%" (
    echo [状态] 服务未运行
    echo PID文件不存在: %PID_FILE%
    echo.
    pause
    exit /b 1
)

:: 读取PID
set /p PID=<"%PID_FILE%"
echo PID文件: %PID_FILE%
echo 进程ID: %PID%
echo.

:: 检查进程是否存在
tasklist /fi "PID eq %PID%" 2>nul | find "%PID%" >nul
if !errorlevel! neq 0 (
    echo [状态] 服务已停止
    echo PID文件存在但进程不存在，建议清理PID文件
    echo.
    choice /c YN /m "是否清理PID文件? (Y/N)"
    if !errorlevel! equ 1 (
        del "%PID_FILE%" 2>nul
        echo PID文件已清理
    )
    echo.
    pause
    exit /b 1
)

:: 获取进程详细信息
echo [状态] 服务正在运行
echo.
echo 进程详细信息:
tasklist /fi "PID eq %PID%" /fo table
echo.

:: 显示内存使用情况
for /f "tokens=5" %%a in ('tasklist /fi "PID eq %PID%" /fo csv ^| findstr "%PID%"') do (
    set MEMORY=%%a
    set MEMORY=!MEMORY:"=!
    set MEMORY=!MEMORY:,=!
    echo 内存使用: !MEMORY! KB
)

:: 检查日志文件
set LOG_FILE=%APP_HOME%\logs\dispatch-server.log
if exist "%LOG_FILE%" (
    echo 日志文件: %LOG_FILE%
    for %%a in ("%LOG_FILE%") do (
        echo 日志大小: %%~za 字节
        echo 最后修改: %%~ta
    )
    echo.
    echo 最近日志内容:
    echo ===============================================
    powershell "Get-Content '%LOG_FILE%' -Tail 10"
    echo ===============================================
) else (
    echo 日志文件不存在: %LOG_FILE%
)

echo.
echo 管理命令:
echo   bin\start.bat    - 启动服务
echo   bin\stop.bat     - 停止服务
echo   bin\dispatch-server.bat logs - 查看更多日志
echo.
pause
exit /b 0
