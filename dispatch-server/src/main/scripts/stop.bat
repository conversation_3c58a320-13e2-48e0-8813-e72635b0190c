@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 设置窗口标题
title Dispatch API 服务停止器

echo.
echo ===============================================
echo           Dispatch API 服务停止器
echo ===============================================
echo.

:: 配置信息
set APP_NAME=dispatch-api

:: 获取程序根目录
cd /d %~dp0
cd ..
set APP_HOME=%cd%

echo 应用目录: %APP_HOME%
echo.

:: 检查PID文件
set PID_FILE=%APP_HOME%\logs\%APP_NAME%.pid
if not exist "%PID_FILE%" (
    echo [信息] 服务未运行或PID文件不存在
    echo.
    pause
    exit /b 0
)

:: 读取PID
set /p PID=<"%PID_FILE%"
echo 找到进程ID: %PID%

:: 检查进程是否存在
tasklist /fi "PID eq %PID%" 2>nul | find "%PID%" >nul
if !errorlevel! neq 0 (
    echo [信息] 进程已不存在，清理PID文件
    del "%PID_FILE%" 2>nul
    echo.
    pause
    exit /b 0
)

echo 正在停止服务...

:: 尝试优雅停止
taskkill /pid %PID% >nul 2>&1
timeout /t 5 >nul

:: 检查是否已停止
tasklist /fi "PID eq %PID%" 2>nul | find "%PID%" >nul
if !errorlevel! neq 0 (
    echo 服务已成功停止
    del "%PID_FILE%" 2>nul
    echo.
    pause
    exit /b 0
)

:: 强制停止
echo 正在强制停止服务...
taskkill /pid %PID% /f >nul 2>&1
timeout /t 3 >nul

:: 最终检查
tasklist /fi "PID eq %PID%" 2>nul | find "%PID%" >nul
if !errorlevel! neq 0 (
    echo 服务已强制停止
    del "%PID_FILE%" 2>nul
) else (
    echo [错误] 无法停止服务，请手动处理
    echo 进程ID: %PID%
)

echo.
pause
exit /b 0
