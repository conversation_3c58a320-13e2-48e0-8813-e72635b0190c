<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hvisions</groupId>
        <artifactId>dispatch</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dispatch-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        后端 Server 的主项目，通过引入需要 dispatch-module-xxx 的依赖，
        从而实现提供 RESTful API 给 dispatch-ui-admin、dispatch-ui-user 等前端项目。
        本质上来说，它就是个空壳（容器）！
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-module-infra-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 样品冷冻库 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-module-task-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-protection</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 打包为tar.gz -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                    <!--<finalName>dispatch-api-${project.version}</finalName>-->
                    <finalName>dispatch-api-new</finalName>
                    <appendAssemblyId>false</appendAssemblyId>
                    <outputDirectory>${project.build.directory}</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
