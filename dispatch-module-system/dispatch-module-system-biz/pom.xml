<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hvisions</groupId>
        <artifactId>dispatch-module-system</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dispatch-module-system-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>com.xingyuv</groupId>
            <artifactId>spring-boot-starter-justauth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId> <!-- 微信登录（公众号） -->
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>  <!-- 微信登录（小程序） -->
        </dependency>

        <dependency>
            <groupId>com.xingyuv</groupId>
            <artifactId>spring-boot-starter-captcha-plus</artifactId> <!-- 验证码，一般用于登录使用 -->
        </dependency>

        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-extra</artifactId> <!-- 邮件 -->
        </dependency>

    </dependencies>

</project>
