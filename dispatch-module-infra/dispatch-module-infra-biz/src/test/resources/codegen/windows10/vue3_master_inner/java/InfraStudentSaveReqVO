package com.hvisions.boot.module.infra.controller.admin.demo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.hvisions.boot.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import com.hvisions.boot.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;

@Schema(description = "管理后台 - 学生新增/修改 Request VO")
@Data
public class InfraStudentSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋头")
    @NotEmpty(message = "名字不能为空")
    private String name;

    @Schema(description = "简介", requiredMode = Schema.RequiredMode.REQUIRED, example = "我是介绍")
    @NotEmpty(message = "简介不能为空")
    private String description;

    @Schema(description = "出生日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出生日期不能为空")
    private LocalDateTime birthday;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "性别不能为空")
    private Integer sex;

    @Schema(description = "是否有效", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否有效不能为空")
    private Boolean enabled;

    @Schema(description = "头像", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/1.png")
    @NotEmpty(message = "头像不能为空")
    private String avatar;

    @Schema(description = "附件", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/1.mp4")
    @NotEmpty(message = "附件不能为空")
    private String video;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "我是备注")
    @NotEmpty(message = "备注不能为空")
    private String memo;

    @Schema(description = "学生联系人列表")
    private List<InfraStudentContactDO> studentContacts;

    @Schema(description = "学生班主任")
    private InfraStudentTeacherDO studentTeacher;

}