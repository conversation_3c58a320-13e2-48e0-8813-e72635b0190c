package com.hvisions.boot.module.infra.dal.mysql.demo;

import java.util.*;

import com.hvisions.boot.framework.common.pojo.PageResult;
import com.hvisions.boot.framework.common.pojo.PageParam;
import com.hvisions.boot.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hvisions.boot.framework.mybatis.core.mapper.BaseMapperX;
import com.hvisions.boot.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生联系人 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentContactMapper extends BaseMapperX<InfraStudentContactDO> {

    default List<InfraStudentContactDO> selectListByStudentId(Long studentId) {
        return selectList(InfraStudentContactDO::getStudentId, studentId);
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentContactDO::getStudentId, studentId);
    }

}