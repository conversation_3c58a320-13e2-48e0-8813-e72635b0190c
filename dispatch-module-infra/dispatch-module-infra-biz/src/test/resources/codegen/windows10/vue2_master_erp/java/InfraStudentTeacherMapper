package com.hvisions.boot.module.infra.dal.mysql.demo;

import java.util.*;

import com.hvisions.boot.framework.common.pojo.PageResult;
import com.hvisions.boot.framework.common.pojo.PageParam;
import com.hvisions.boot.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hvisions.boot.framework.mybatis.core.mapper.BaseMapperX;
import com.hvisions.boot.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生班主任 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentTeacherMapper extends BaseMapperX<InfraStudentTeacherDO> {

    default PageResult<InfraStudentTeacherDO> selectPage(PageParam reqVO, Long studentId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfraStudentTeacherDO>()
            .eq(InfraStudentTeacherDO::getStudentId, studentId)
            .orderByDesc(InfraStudentTeacherDO::getId));
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentTeacherDO::getStudentId, studentId);
    }

}