import request from '@/utils/request'

// 创建分类
export function createCategory(data) {
  return request({
    url: '/infra/category/create',
    method: 'post',
    data: data
  })
}

// 更新分类
export function updateCategory(data) {
  return request({
    url: '/infra/category/update',
    method: 'put',
    data: data
  })
}

// 删除分类
export function deleteCategory(id) {
  return request({
    url: '/infra/category/delete?id=' + id,
    method: 'delete'
  })
}

// 获得分类
export function getCategory(id) {
  return request({
    url: '/infra/category/get?id=' + id,
    method: 'get'
  })
}

// 获得分类列表
export function getCategoryList(params) {
  return request({
    url: '/infra/category/list',
    method: 'get',
    params
  })
}
// 导出分类 Excel
export function exportCategoryExcel(params) {
  return request({
    url: '/infra/category/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
