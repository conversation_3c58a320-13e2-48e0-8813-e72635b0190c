[{"contentPath": "java/InfraCategoryListReqVO", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/main/java/com.hvisions.boot/module/infra/controller/admin/demo/vo/InfraCategoryListReqVO.java"}, {"contentPath": "java/InfraCategoryRespVO", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/main/java/com.hvisions.boot/module/infra/controller/admin/demo/vo/InfraCategoryRespVO.java"}, {"contentPath": "java/InfraCategorySaveReqVO", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/main/java/com.hvisions.boot/module/infra/controller/admin/demo/vo/InfraCategorySaveReqVO.java"}, {"contentPath": "java/InfraCategoryController", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/main/java/com.hvisions.boot/module/infra/controller/admin/demo/InfraCategoryController.java"}, {"contentPath": "java/InfraCategoryDO", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/main/java/com.hvisions.boot/module/infra/dal/dataobject/demo/InfraCategoryDO.java"}, {"contentPath": "java/InfraCategoryMapper", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/main/java/com.hvisions.boot/module/infra/dal/mysql/demo/InfraCategoryMapper.java"}, {"contentPath": "xml/InfraCategoryMapper", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/main/resources/mapper/demo/InfraCategoryMapper.xml"}, {"contentPath": "java/InfraCategoryServiceImpl", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/main/java/com.hvisions.boot/module/infra/service/demo/InfraCategoryServiceImpl.java"}, {"contentPath": "java/InfraCategoryService", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/main/java/com.hvisions.boot/module/infra/service/demo/InfraCategoryService.java"}, {"contentPath": "java/InfraCategoryServiceImplTest", "filePath": "cdc-module-infra/cdc-module-infra-biz/src/test/java/com.hvisions.boot/module/infra/service/demo/InfraCategoryServiceImplTest.java"}, {"contentPath": "java/ErrorCodeConstants_手动操作", "filePath": "cdc-module-infra/cdc-module-infra-api/src/main/java/com.hvisions.boot/module/infra/enums/ErrorCodeConstants_手动操作.java"}, {"contentPath": "sql/sql", "filePath": "sql/sql.sql"}, {"contentPath": "sql/h2", "filePath": "sql/h2.sql"}, {"contentPath": "vue/index", "filePath": "cdc-ui-admin-vue2/src/views/infra/demo/index.vue"}, {"contentPath": "js/index", "filePath": "cdc-ui-admin-vue2/src/api/infra/demo/index.js"}, {"contentPath": "vue/CategoryForm", "filePath": "cdc-ui-admin-vue2/src/views/infra/demo/CategoryForm.vue"}]