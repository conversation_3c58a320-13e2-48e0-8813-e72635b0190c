package com.hvisions.boot.module.infra.controller.admin.demo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.hvisions.boot.framework.common.pojo.PageParam;
import com.hvisions.boot.framework.common.pojo.PageResult;
import com.hvisions.boot.framework.common.pojo.CommonResult;
import com.hvisions.boot.framework.common.util.object.BeanUtils;
import static com.hvisions.boot.framework.common.pojo.CommonResult.success;

import com.hvisions.boot.framework.excel.core.util.ExcelUtils;

import com.hvisions.boot.framework.operatelog.core.annotations.OperateLog;
import static com.hvisions.boot.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.hvisions.boot.module.infra.controller.admin.demo.vo.*;
import com.hvisions.boot.module.infra.dal.dataobject.demo.InfraStudentDO;
import com.hvisions.boot.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import com.hvisions.boot.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;
import com.hvisions.boot.module.infra.service.demo.InfraStudentService;

@Tag(name = "管理后台 - 学生")
@RestController
@RequestMapping("/infra/student")
@Validated
public class InfraStudentController {

    @Resource
    private InfraStudentService studentService;

    @PostMapping("/create")
    @Operation(summary = "创建学生")
    @PreAuthorize("@ss.hasPermission('infra:student:create')")
    public CommonResult<Long> createStudent(@Valid @RequestBody InfraStudentSaveReqVO createReqVO) {
        return success(studentService.createStudent(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新学生")
    @PreAuthorize("@ss.hasPermission('infra:student:update')")
    public CommonResult<Boolean> updateStudent(@Valid @RequestBody InfraStudentSaveReqVO updateReqVO) {
        studentService.updateStudent(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除学生")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('infra:student:delete')")
    public CommonResult<Boolean> deleteStudent(@RequestParam("id") Long id) {
        studentService.deleteStudent(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得学生")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('infra:student:query')")
    public CommonResult<InfraStudentRespVO> getStudent(@RequestParam("id") Long id) {
        InfraStudentDO student = studentService.getStudent(id);
        return success(BeanUtils.toBean(student, InfraStudentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得学生分页")
    @PreAuthorize("@ss.hasPermission('infra:student:query')")
    public CommonResult<PageResult<InfraStudentRespVO>> getStudentPage(@Valid InfraStudentPageReqVO pageReqVO) {
        PageResult<InfraStudentDO> pageResult = studentService.getStudentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InfraStudentRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出学生 Excel")
    @PreAuthorize("@ss.hasPermission('infra:student:export')")
    @OperateLog(type = EXPORT)
    public void exportStudentExcel(@Valid InfraStudentPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InfraStudentDO> list = studentService.getStudentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "学生.xls", "数据", InfraStudentRespVO.class,
                        BeanUtils.toBean(list, InfraStudentRespVO.class));
    }

    // ==================== 子表（学生联系人） ====================

    @GetMapping("/student-contact/list-by-student-id")
    @Operation(summary = "获得学生联系人列表")
    @Parameter(name = "studentId", description = "学生编号")
    @PreAuthorize("@ss.hasPermission('infra:student:query')")
    public CommonResult<List<InfraStudentContactDO>> getStudentContactListByStudentId(@RequestParam("studentId") Long studentId) {
        return success(studentService.getStudentContactListByStudentId(studentId));
    }

    // ==================== 子表（学生班主任） ====================

    @GetMapping("/student-teacher/get-by-student-id")
    @Operation(summary = "获得学生班主任")
    @Parameter(name = "studentId", description = "学生编号")
    @PreAuthorize("@ss.hasPermission('infra:student:query')")
    public CommonResult<InfraStudentTeacherDO> getStudentTeacherByStudentId(@RequestParam("studentId") Long studentId) {
        return success(studentService.getStudentTeacherByStudentId(studentId));
    }

}