package com.hvisions.boot.module.infra.dal.dataobject.demo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.hvisions.boot.framework.mybatis.core.dataobject.BaseDO;

/**
 * 学生联系人 DO
 *
 * <AUTHOR>
 */
@TableName("infra_student_contact")
@KeySequence("infra_student_contact_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfraStudentContactDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 学生编号
     */
    private Long studentId;
    /**
     * 名字
     */
    private String name;
    /**
     * 简介
     */
    private String description;
    /**
     * 出生日期
     */
    private LocalDateTime birthday;
    /**
     * 性别
     *
     * 枚举 {@link TODO system_user_sex 对应的类}
     */
    private Integer sex;
    /**
     * 是否有效
     *
     * 枚举 {@link TODO infra_boolean_string 对应的类}
     */
    private Boolean enabled;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 附件
     */
    private String video;
    /**
     * 备注
     */
    private String memo;

}