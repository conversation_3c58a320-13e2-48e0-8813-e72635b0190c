package com.hvisions.boot.module.task.enums.order;

import com.hvisions.boot.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum OrderTypeEnum implements IntArrayValuable {

    //1 入库 2出库
    IN_STOCK(1, "入库"),
    OUT_STOCK(2, "出库")
    ;

    private final Integer type;
    private final String name;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(OrderTypeEnum::getType).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
