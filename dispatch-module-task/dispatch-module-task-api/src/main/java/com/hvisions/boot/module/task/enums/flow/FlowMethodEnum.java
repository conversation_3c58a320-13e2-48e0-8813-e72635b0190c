package com.hvisions.boot.module.task.enums.flow;

import com.hvisions.boot.module.task.enums.task.TaskExecuteStepEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 流程定义
 * @Date: 2025/3/7 23:55
 * @Author: zhangq
 * @Version: 1.0
 */
@Getter
@AllArgsConstructor
public enum FlowMethodEnum {

    //公用步骤
    //入库订单开始
    START_IN_TASK_PROCESS("startInTaskProcess", "开始任务流程"),
    //传递窗公用
    WINDOW_DOOR_CLOSED_IN("windowDoorCloseIn", "传递窗上料/下料完毕内门关闭"),
    //传递窗上料
    CHECK_AGV_IDLE("checkAgvIdle", "检查AGV是否空闲"),
    CHECK_TRANSFER_READY("checkTransferReady", "检查传递窗是否就绪"),
    AGV_MOVING_WINDOW_IN("agvMovingWindowIn", "AGV移动到传递窗-上料"),
    CALLBACK_TASK("callbackTask", "回调上层管理软件"),
    AGV_ARRIVED_WINDOW_IN("agvArrivedWindowIn", "AGV到达传递窗-上料"),
    WINDOW_OPEN_INNER_DOOR("windowOpenInnerDoor", "传递窗内门打开"),
    WINDOW_DOOR_OPEN_COMPLETE("windowDoorOpenComplete", "传递窗内门打开完成"),
    WINDOW_OUT_BELT_RUNNING("windowInBeltRunning", " 内门位皮带输出启动"),
    WINDOW_OUT_BELT_STOP("windowInBeltStop", "内门位皮带输出完成"),
    WINDOW_DOOR_CLOSED_OUT("windowDoorCloseOut", "传递窗下料完毕外门关闭"),
    AGV_TANK_ARRIVED("agvInTankArrived", "AGV反馈收到中转罐"),
    NEXT_TASK_ISSUED("nextTaskIssued", "下发下一任务"),

    //结果回调
    TASK_RESULT_COMPLETE("taskResultComplete", "结果回调"),

    //大库公用
    //大库单盒仓门打开
    FREEZER_OPEN_SINGLE_DOOR("freezerOpenSingleDoor", "大库单盒仓门打开"),
    //单盒舱门打开到位
    FREEZER_OPEN_SINGLE_DOOR_COMPLETE("freezerOpenSingleDoorComplete", "单盒仓门打开到位"),
    //大库关门
    FREEZER_CLOSE_SINGLE_DOOR("freezerCloseSingleDoor", "大库单盒仓门关闭"),
    //大库单盒仓门关闭到位
    FREEZER_CLOSE_SINGLE_DOOR_COMPLETE("freezerCloseSingleDoorComplete", "大库单盒仓门关闭到位"),
    //同意agv上单盒板架
    AG_AGREE_SINGLE_BOX("agvAgreeSingleBox", "同意agv上单盒板架"),

    //大库入
    AGV_TO_FREEZER_IN("agvToFreezerIn", "AGV到达大库定位点-入"),
    //agv反馈上板架完成
    AGV_IN_SINGLE_BOX_COMPLETE("agvInSingleBoxComplete", "AGV上单盒板架完成"),
    //大库扫码结果
    FREEZER_IN_SCAN_RESULT("freezerInScanResult", "大库扫码结果"),
    //扫码失败单盒到达
    FREEZER_IN_SCAN_FAIL_ARRIVE("freezerInScanFailArrive", "扫码失败单盒到达舱门"),
    //扫码失败单盒出任务
    AGV_MOVING_FREEZER_OUT("agvMovingFreezerOut", "扫码失败单盒任务-下料"),

    //大库出
    AGV_TO_FREEZER_OUT("agvToFreezerOut", "AGV到达大库定位点-出"),
    //同意agv上单盒板架夹盒
    AGV_OUT_SINGLE_BOX("agvOutSingleBox", "AGV上单盒板架夹盒"),
    //agv上单盒板架夹盒完成
    AGV_OUT_SINGLE_BOX_COMPLETE("agvOutSingleBoxComplete", "AGV上单盒板架夹盒完成"),
    //大库出库完成
    FREEZER_OUT_TASK_COMPLETE("freezerOutTaskComplete", "大库出库完成"),
    //agv任务完成
    FREEZER_AGV_OUT_TASK_COMPLETE("freezerAgvOutTaskComplete", "大库出库AGV动作完成"),
    //出库缺盒开门
    FREEZER_OUT_OPEN_SINGLE_COMPLETE("freezerOutOpenSingleComplete", "出库缺盒开门反馈"),
    //出库缺盒关门
    FREEZER_OUT_CLOSE_SINGLE_COMPLETE("freezerOutCloseSingleComplete", "出库缺盒关门反馈"),
    //盒子移动至单盒舱门
    FREEZER_BOX_MOVE_SINGLE_DOOR("freezerBoxMoveSingleDoor", "盒子移动至单盒舱门"),
    //大库扫码结果
    FREEZER_OUT_SCAN_RESULT("freezerOutScanResult", "大库出库扫码结果"),
    //预约出库任务执行完成待取货
    FREEZER_RESERVE_OUT_TASK_COMPLETE("freezerOutTaskComplete", "预约出库任务执行完成待取货"),
    //预约出库任务执行完成取货完成
    FREEZER_RESERVE_OUT_TASK_COMPLETE_COMPLETE("freezerReserveOutTaskCompleteComplete", "预约出库任务执行完成取货完成"),

    //液氮罐
    AGV_MOVING_HCRYO("agvMovingHCryo", "AGV移动到液氮罐"),
    //通知液氮罐接罐
    HCRYO_TANK_AGV_READY("hCryoTankAgvReady", "AGV到达液氮罐指定位置"),
    //液氮罐接到中转罐
    HCRYO_TANK_IN_ARRIVED("hCryoTankInArrived", "液氮罐接到中转罐"),
    //扫码结果
    HCRYO_SCAN_RESULT("hCryoScanResult", "液氮罐扫码结果"),
    //液氮罐任务结束
    AGV_HCRYO_TASK_COMPLETE("agvHCryoTaskComplete", "AGV到液氮罐任务结束"),
    //液氮罐任务结束
    HCRYO_TASK_COMPLETE("hCryoTaskComplete", "液氮罐任务结束"),
    //agv移动到液氮罐接罐
    AGV_MOVING_HCRYO_OUT("agvMovingHCryoOut", "下发AGV到液氮罐取中转罐"),
    //agv移动到液氮罐接罐完成
    AGV_MOVING_HCRYO_OUT_COMPLETE("agvMovingHCryoOutComplete", "AGV移动到液氮罐通知液氮罐放罐"),
    //agv接收到中转罐
    AGV_TANK_IN_HCRYO("agvTankInHCryo", "AGV接收到中转罐"),
    //agv接收到液氮罐
    HCRYO_IN_TANK_AGV("hCryoTankInAgv", "液氮罐输送中转罐至AGV完成"),

    //传递窗下料
    //AGV到传递窗
    AGV_MOVING_WINDOW_OUT("agvMovingWindowOut", "AGV移动到传递窗-下料"),
    //AGV到达传递窗-下料
    AGV_ARRIVED_WINDOW_OUT("agvArrivedWindowOut", "AGV到达传递窗-下料"),
    //内门位皮带输入启动
    WINDOW_IN_BELT_RUNNING("windowInBeltRunning", "内门位皮带输入启动"),
    //内门位皮带输入完成
    WINDOW_IN_BELT_STOP("windowInBeltStop", "内门位皮带输出完成"),
    CALLBACK_TASK_RESULT("callbackTaskResult", "回调上层管理软件-任务结果"),
    //agv下料完成
    AGV_IN_WINDOW_COMPLETE("agvInWindowComplete", "AGV下料完成"),
    ;

    private final String methodName;

    private final String stepName;

    public static String getStepName(String methodName) {
        for (TaskExecuteStepEnum stepEnum : TaskExecuteStepEnum.values()) {
            if (stepEnum.getMethodName().equals(methodName)) {
                return stepEnum.getStepName();
            }
        }
        return methodName;
    }
}
