package com.hvisions.boot.module.task.enums.agv;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: TODO
 * @Date: 2025/3/18 16:43
 * @Author: zhangq
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum AGVOpReportStatusEnum {

    //当前任务步骤
    //1:已下发
    //2:取货请求
    //3:取货完成
    //4:卸货请求
    //5:卸货完成
    //6:机械臂取货请求
    //7:机械臂取货完成
    //8:机械臂卸货请求
    //9:机械臂卸货完成
    PICK_UP_ISSUED("1", "已下发"),
    PICK_UP_REQUEST("2", "取货请求"),
    PICK_UP_COMPLETE("3", "取货完成"),
    UNLOAD_REQUEST("4", "卸货请求"),
    UNLOAD_COMPLETE("5", "卸货完成"),
    //机械臂取货请求
    ARM_PICK_UP_REQUEST("6", "机械臂取货请求"),
    ARM_PICK_UP_COMPLETE("7", "机械臂取货完成"),
    //机械臂卸货请求
    ARM_UNLOAD_REQUEST("8", "机械臂卸货请求"),
    ARM_UNLOAD_COMPLETE("9", "机械臂卸货完成"),
    ;
    /**
     * 类型
     */
    private final String code;
    /**
     * 站点
     */
    private final String msg;

    public static AGVOpReportStatusEnum AgvOrderStatusEnum(String status) {
        for (AGVOpReportStatusEnum stepEnum : AGVOpReportStatusEnum.values()) {
            if (stepEnum.getCode().equals(status)) {
                return stepEnum;
            }
        }
        throw new IllegalArgumentException("非法动作： " + status);
    }
}
