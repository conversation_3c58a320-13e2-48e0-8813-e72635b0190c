package com.hvisions.boot.module.task.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 任务类型枚举
 * @Date: 2025/4/15 15:23
 * @Author: zhangq
 * @Version: 1.0
 */
@Getter
@AllArgsConstructor
public enum TaskTypeEnum {

    //1 入库 2 出库 9预约出库
    IN_STORAGE(1, "入库"),
    OUT_STORAGE(2, "出库"),
    RESERVE_OUT_STORAGE(9, "预约出库");

    private final Integer type;
    private final String name;
}
