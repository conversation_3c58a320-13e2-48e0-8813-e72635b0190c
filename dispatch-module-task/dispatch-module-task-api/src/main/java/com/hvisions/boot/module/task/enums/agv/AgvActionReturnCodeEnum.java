package com.hvisions.boot.module.task.enums.agv;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: agv动作返回码
 * @Date: 2025/3/18 14:08
 * @Author: zhangq
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum AgvActionReturnCodeEnum {

    //调用接口的返回值
    //0：前置设备未准备好
    //500: 异常
    //101：允许取货/卸货
    //102：下发/取货/卸货通知确认
    PREFERENCE_NOT_READY("0", "前置设备未准备好"),
    //站点不存在
    STATION_NOT_EXIST("404", "站点不存在"),
    //非法动作
    EXCEPTION("500", "非法动作"),
    AGREE_PICK_UP_OR_UNLOAD("101", "允许取货/卸货"),
    NOTICE_PICK_UP_OR_UNLOAD("102", "下发/取货/卸货通知确认"),
    ;
    /**
     * 类型
     */
    private final String code;
    /**
     * 站点
     */
    private final String msg;
}
