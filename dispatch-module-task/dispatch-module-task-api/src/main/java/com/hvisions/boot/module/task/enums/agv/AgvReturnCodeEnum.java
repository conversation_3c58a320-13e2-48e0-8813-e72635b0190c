package com.hvisions.boot.module.task.enums.agv;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: TODO
 * @Date: 2025/3/18 14:08
 * @Author: zhangq
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum AgvReturnCodeEnum{

    //调用接口的返回值
    //1：成功
    //2：orderId重复
    //3：orderId为空
    //4：位置信息为空
    //5：任务内的站点在AGV系统中不存在
    //6：任务插入失败
    SUCCESS("0001", "成功"),
    REPEAT_ORDER_ID("0002", "orderId重复"),
    NO_ORDER_ID("0003", "orderId为空"),
    NO_POSITION_INFO("0004", "位置信息为空"),
    NO_POSITION_IN_AGV("0005", "任务内的站点在AGV系统中不存在"),
    NO_INSERT_AGV_TASK("0006", "任务插入失败"),
    ;
    /**
     * 类型
     */
    private final String code;
    /**
     * 站点
     */
    private final String msg;
}
