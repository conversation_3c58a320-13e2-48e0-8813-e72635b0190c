package com.hvisions.boot.module.task.enums.agv;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: AGV任务回调枚举
 * @Date: 2025/3/18 14:08
 * @Author: zhangq
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum AgvTaskCallBackReturnEnum {

    //调用接口的返回值
    //001-任务完成确认
    //002-等待确认
    //305-调用接口失败
    SUCCESS("001", "任务完成确认"),
    WAITING("002", "等待确认"),
    FAILURE("305", "调用接口失败"),
    ;
    /**
     * 类型
     */
    private final String code;
    /**
     * 站点
     */
    private final String msg;
}
