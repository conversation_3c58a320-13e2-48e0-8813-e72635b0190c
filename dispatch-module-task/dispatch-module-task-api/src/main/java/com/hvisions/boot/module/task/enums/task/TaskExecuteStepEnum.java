package com.hvisions.boot.module.task.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: TODO
 * @Date: 2024/12/27 21:54
 * @Author: zhangq
 * @Version: 1.0
 */
@Getter
@AllArgsConstructor
public enum TaskExecuteStepEnum {

    //agv到传递窗
    AGV_TO_WINDOW("agvToWindow", "agv到传递窗"),
    //agv到达传递窗反馈
    AGV_TO_WINDOW_REPLY("agvToWindowReply", "agv到达传递窗"),
    //传递窗内门打开
    WINDOW_OPEN_IN_REPLY("windowOpenInReply", "传递窗内门打开"),
    //出罐传递窗内门打开
    WINDOW_OPEN_IN_OUT_REPLY("windowOpenInOutReply", "出罐传递窗内门打开"),
    //传递窗内门打开完成,内门位皮带输出启动
    WINDOW_IN_BELT_OUT_REPLY("windowInBeltOutReply", "传递窗内门打开完成,内门位皮带输出启动"),
    //传递窗内门打开完成,内门位皮带输入启动
    WINDOW_IN_BELT_IN_REPLY("windowInBeltInReply", "传递窗内门打开完成,内门位皮带输入启动"),
    //AGV反馈收到中转罐到位
    AGV_IN_TANK_ARRIVED_REPLY("agvInTankArrivedReply", "AGV反馈收到中转罐到位"),
    //通知传递窗关内门
    WINDOW_CLOSE_IN_REPLY("windowCloseInReply", "通知传递窗关内门"),
    //通知agv去大库
    AGV_GO_TO_BIG_WAREHOUSE_REPLY("agvGoToBigWarehouseReply", "通知agv去大库"),
    //大库单盒仓门 gigaUltOpenSingleBoxReply
    GIGA_ULT_OPEN_SINGLE_BOX_REPLY("gigaUltOpenSingleBoxReply", "大库单盒仓门"),
    //agv上板架
    AGV_GO_TO_BIG_BAN_JIA_REPLY("agvGoToBigBanJiaReply", "入库agv上板架"),
    //agv上板架执行出库任务
    AGV_GO_TO_BIG_BAN_JIA_OUT_REPLY("agvGoToBigBanJiaOutReply", "agv上板架执行出库任务"),
    //大库关门
    GIGA_ULT_CLOSE_SINGLE_BOX_REPLY("gigaUltCloseSingleBoxReply", "大库关门"),
    //大库扫码结果
    GIGA_ULT_SCAN_RESULT_REPLY("gigaUltScanResultReply", "大库扫码结果"),
    //下发agv前往传递窗送中转罐任务
    AGV_TO_WINDOW_OUT_REPLY("agvToWindowOutReply", "下发agv前往传递窗送中转罐任务"),
    //下发agv前往大库取出冻存盒任务
    AGV_GO_TO_BIG_WAREHOUSE_OUT_REPLY("agvGoToBigWarehouseOutReply", "下发agv前往大库取出冻存盒任务"),
    //agv执行取盒
    AGV_EXECUTE_BOX_OUT_REPLY("agvExecuteBoxOutReply", "agv执行取盒"),
    //agv送中转罐至传递窗
    AGV_OUT_TANK_REPLY("agvOutTankReply", "agv送中转罐至传递窗"),
    //agv出库夹盒
    AGV_CLAMP_BOX_OUT_REPLY("agvClampBoxOutReply", "agv出库夹盒"),
    //出库扫码
    GIGA_ULT_SCAN_RESULT_OUT_REPLY("gigaUltScanResultOutReply", "出库大库扫码结果"),

    //============= 超低温 ==============
    //通知agv去液氮罐
    AGV_GO_TO_HCRYO_REPLY("agvGoHCryoReply", "通知agv去液氮罐"),
    //通知液氮罐接罐
    HCRYO_IN_REPLY("HCryoInReply", "通知液氮罐接罐"),
    //液氮罐扫码结果
    HCRYO_SCAN_RESULT_REPLY("HCryoScanResultReply", "液氮罐扫码结果"),
    //通知agv去液氮罐出
    AGV_GO_TO_HCRYO_OUT_REPLY("agvGoHCryoOutReply", "通知agv去液氮罐出"),


    //结果回调
    TASK_RESULT_REPLY("taskResultReoly", "结果回调"),
    ;

    private final String methodName;

    private final String stepName;

    public static String getStepName(String methodName) {
        for (TaskExecuteStepEnum stepEnum : TaskExecuteStepEnum.values()) {
            if (stepEnum.getMethodName().equals(methodName)) {
                return stepEnum.getStepName();
            }
        }
        return methodName;
    }

}
