package com.hvisions.boot.module.task.enums.task;

import com.hvisions.boot.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务回调状态枚举
 *
 */
@Getter
@AllArgsConstructor
public enum TaskCallbackStatusEnum implements IntArrayValuable {

    WAITING("1", "待执行"),
    PICKING("2", "待取货"),
    EXECUTING("4", "执行中"),
    AGV_RUNNING("3", "AGV运行中"),
    EXECUTED("5", "执行完成"),
    ABNORMAL("6", "异常"),
    SCAN_FAIL("7", "扫码失败"),
    CANCEL("8", "取消"),
    ;

    private final String status;
    private final String name;

    @Override
    public int[] array() {
        return new int[0];
    }
}
