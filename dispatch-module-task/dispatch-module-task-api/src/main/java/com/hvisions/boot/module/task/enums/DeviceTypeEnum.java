package com.hvisions.boot.module.task.enums;

import com.hvisions.boot.framework.common.core.IntArrayValuable;
import com.hvisions.boot.framework.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

@AllArgsConstructor
@Getter
public enum DeviceTypeEnum implements IntArrayValuable {


    /**
     * 大库1号
     */
    //GIGAULT003(1,"station2", "GigaUlt003", "************:8081", "大库1号","超低温自动化一号库", -80.0),
    GIGAULT003(1,"station2", "hiperUlt001", "*************:8082", "冰箱1号","超低温自动化一号冰箱", -80.0),
    /**
     * 大库2号
     */
    GIGAULT004(1, "station3","GigaUlt004", "************:8081", "大库2号","超低温自动化二号库", -80.0),

    /**
     * 液氮罐1号
     */
    HCryo240126001(2, "station4","HCryo240126001", "************:8086","液氮罐1号","深低温自动化一号库",-180.0),

    /**
     * 液氮罐2号
     */
    HCryo240126002(2, "station5","HCryo240126002", "************:8086", "液氮罐2号","深低温自动化二号库", -180.0);

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 站点
     */
    private final String station;
    /**
     * 类型名
     */
    private final String deviceCode;

    /**
     * 地址
     */
    private final String url;

    /**
     * 设备名称
     */
    private final String deviceName;

    /**
     * 设备描述
     */
    private final String deviceDesc;

    /**
     * 温度
     */
    private final Double temperature;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DeviceTypeEnum::getType).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static List<String> getDeviceCodes() {
        return Arrays.stream(values()).map(DeviceTypeEnum::getDeviceCode).toList();
    }

    public static List<String> getStations() {
        return Arrays.stream(values()).map(DeviceTypeEnum::getStation).toList();
    }

    public static DeviceTypeEnum validateStation(String station) {
        for (DeviceTypeEnum value : DeviceTypeEnum.values()) {
            if (value.getStation().equals(station)) {
                return value;
            }
        }
        throw new ServiceException(500,"设备站点"+station+"不存在");
    }

    public static DeviceTypeEnum validateDeviceType(String deviceType) {
        for (DeviceTypeEnum value : DeviceTypeEnum.values()) {
            if (value.getDeviceCode().equals(deviceType)) {
                return value;
            }
        }
       throw new ServiceException(500,"设备类型"+deviceType+"不存在");
    }

    public static DeviceTypeEnum validateDeviceCode(String deviceCode) {
        for (DeviceTypeEnum value : DeviceTypeEnum.values()) {
            if (value.getDeviceCode().equals(deviceCode)) {
                return value;
            }
        }
        throw new ServiceException(500,"设备编码"+deviceCode+"不存在");
    }

    public static List<DeviceTypeEnum> validateDeviceCodes(Integer type) {
        List<DeviceTypeEnum> list = Arrays.stream(values()).filter(e -> e.getType().equals(type)).toList();
        if (CollectionUtils.isEmpty(list)){
            throw new ServiceException(500,"设备类型"+type+"不存在");
        }
        return list;
    }
}