package com.hvisions.boot.module.task.enums.freezer;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大库回调状态枚举
 *
 */
@Getter
@AllArgsConstructor
public enum FreezerActionEnum {
    /**
     * 入库开门关门1,2
     * 出库开门关门3,4
     * 入库任务完成5
     * 出库任务完成6
     * 预约出库任务完成7
     * 盒子移动至单盒舱门8
     * 入库盒子到储位完成 9
     * 出库缺盒开门关门10,11
     * 预约取货任务完成12
     * 扫码结果13
     * 预约取货任务开门关门14,15
     */
    IN_OPEN_DOOR("1", "入库开门"),
    IN_CLOSE_DOOR("2", "入库关门"),
    OUT_OPEN_DOOR("3", "出库开门"),
    OUT_CLOSE_DOOR("4", "出库关门"),
    IN_TASK_COMPLETE("5", "入库任务完成"),
    OUT_TASK_COMPLETE("6", "出库任务完成"),
    BOX_MOVE_SINGLE_DOOR("8", "盒子移动至单盒舱门"),
    SCAN_RESULT("13", "扫码结果"),
    IN_BOX_ARRIVED("9", "入库盒子到储位完成"),

    RESERVE_OUT_TASK_COMPLETE("7", "预约出库任务完成"),
    OUT_EMPTY_BOX_OPEN_DOOR("10", "出库缺盒开门"),
    OUT_EMPTY_BOX_CLOSE_DOOR("11", "出库缺盒关门"),
    RESERVE_OUT_TASK_OPEN_DOOR("14", "预约取货任务开门"),
    RESERVE_OUT_TASK_CLOSE_DOOR("15", "预约取货任务关门"),
    RESERVE_PICKUP_TASK_COMPLETE("12", "预约取货任务完成"),
    ;

    private final String action;
    private final String name;
}
