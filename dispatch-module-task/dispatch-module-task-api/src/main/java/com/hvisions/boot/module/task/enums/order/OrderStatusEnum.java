package com.hvisions.boot.module.task.enums.order;

import com.hvisions.boot.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 状态枚举
 *
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum implements IntArrayValuable {

    WAITING(0, "待执行"),
    RUNNING(100, "执行中"),
    SUCCESS(200, "成功"),
    ABNORMAL(500, "程序异常"),
    FAIL(400, "扫码失败"),
    CLOSED(300, "关闭"),
    CANCELLED(600, "挑管中"),
    EXECUTED_SUCCESS_CLOSED(900, "执行成功关闭"),
    //存在待执行任务 - 多盒
    EXECUTING(700, "执行中"),
    //任务挂起
    SUSPENDED(99, "挂起"),
    ;

    private final Integer status;
    private final String name;

    @Override
    public int[] array() {
        return new int[0];
    }

    /**
     * 判断是否等待
     *
     * @param status 状态
     * @return 是否等待支付
     */
    public static boolean isWaiting(Integer status) {
        return Objects.equals(status, WAITING.getStatus());
    }

    /**
     * 判断是否成功
     *
     * @param status 状态
     * @return 是否成功
     */
    public static boolean isSuccess(Integer status) {
        return Objects.equals(status, SUCCESS.getStatus());
    }

    /**
     * 判断是否异常
     *
     * @param status 状态
     * @return 是否异常
     */
    public static boolean isFail(Integer status) {
        return Objects.equals(status, ABNORMAL.getStatus());
    }

    /**
     * 判断关闭
     *
     * @param status 状态
     * @return 是否关闭
     */
    public static boolean isClosed(Integer status) {
        return Objects.equals(status, CLOSED.getStatus());
    }

}
