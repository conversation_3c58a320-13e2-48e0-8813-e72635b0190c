package com.hvisions.boot.module.task.enums.task;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {
    //PENDING, IN_PROGRESS, COMPLETED, FAILED
    PENDING(0, "待执行"),
    IN_PROGRESS(1, "执行中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "执行失败"),
    ABORTED(4, "已终止"),
    //已处理
    HANDLED(5, "已处理"),
    //处理中
    HANDLING(6, "处理中"),//液氮罐独有
    ;

    private final Integer status;
    private final String name;
}
