package com.hvisions.boot.module.task.enums.agv;

import com.hvisions.boot.framework.common.core.IntArrayValuable;
import com.hvisions.boot.framework.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: TODO
 * @Date: 2025/3/4 22:13
 * @Author: zhangq
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum CarStatusEnum implements IntArrayValuable {

    //状态（离线/手动/自动/充电中/空闲/繁忙）
    OFFLINE(0, "离线"),
    MANUAL(1, "手动"),
    AUTO(2, "自动"),
    CHARGING(3, "充电中"),
    IDLE(4, "空闲"),
    BUSY(5, "繁忙"),
    ;
    /**
     * 类型
     */
    private final Integer status;
    /**
     * 站点
     */
    private final String msg;


    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CarStatusEnum::getStatus).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static CarStatusEnum validateStatus(Integer status) {
        for (CarStatusEnum value : CarStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        throw new ServiceException(500,"agv状态"+status+"不存在");
    }

    public static CarStatusEnum validateAgvMsg(String msg) {
        for (CarStatusEnum value : CarStatusEnum.values()) {
            if (value.getMsg().equals(msg)) {
                return value;
            }
        }
        throw new ServiceException(500,"agv状态描述"+msg+"不存在");
    }
}
