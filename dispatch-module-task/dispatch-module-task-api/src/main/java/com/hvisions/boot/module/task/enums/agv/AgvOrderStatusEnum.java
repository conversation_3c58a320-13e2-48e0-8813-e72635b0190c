package com.hvisions.boot.module.task.enums.agv;

import com.hvisions.boot.framework.common.core.IntArrayValuable;
import com.hvisions.boot.framework.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: TODO
 * @Date: 2025/3/4 22:13
 * @Author: zhangq
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum AgvOrderStatusEnum implements IntArrayValuable {

    //状态 运行中 挂起 可执行 结束
    RUNNING(100, "运行中"),
    HANG(200, "挂起"),
    EXECUTABLE(300, "可执行"),
    END(400, "结束"),
    ;
    /**
     * 类型
     */
    private final Integer status;
    /**
     * 站点
     */
    private final String msg;


    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(AgvOrderStatusEnum::getStatus).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static AgvOrderStatusEnum validateStatus(Integer status) {
        for (AgvOrderStatusEnum value : AgvOrderStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        throw new ServiceException(500,"agv状态"+status+"不存在");
    }

    public static AgvOrderStatusEnum validateAgvMsg(String msg) {
        for (AgvOrderStatusEnum value : AgvOrderStatusEnum.values()) {
            if (value.getMsg().equals(msg)) {
                return value;
            }
        }
        throw new ServiceException(500,"agv状态描述"+msg+"不存在");
    }
}
