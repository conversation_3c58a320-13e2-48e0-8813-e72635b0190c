package com.hvisions.boot.module.task.enums;

import com.hvisions.boot.framework.common.exception.ErrorCode;

/**
 * freezer 错误码枚举类
 *
 * freezer 系统，使用 1-001-000-000 段
 */
public interface ErrorCodeConstants {


    // ========== ORDER 模块 1-007-002-000 ==========
    ErrorCode TASK_ORDER_NOT_FOUND = new ErrorCode(1_007_002_000, "任务订单不存在");
    ErrorCode TASK_ORDER_STATUS_IS_NOT_WAITING = new ErrorCode(1_007_002_001, "订单不处于待处理");

    //任务正在执行中，请稍后再试
    ErrorCode TASK_ORDER_IS_EXECUTING = new ErrorCode(1_007_002_002, "当前有任务正在执行中，请稍后再试");
    //工作站不存在
    ErrorCode WORK_STATION_NOT_FOUND = new ErrorCode(1_007_002_015, "工站不存在");
    //流程步骤不存在
    ErrorCode TASK_FLOW_STEP_NOT_FOUND = new ErrorCode(1_007_002_003, "流程步骤不存在");
    ErrorCode TASK_FLOW_STEP_IS_NOT = new ErrorCode(1_007_002_004, "流程步骤已不存在");
    //打开传递窗内门失败
    ErrorCode TASK_FLOW_STEP_OPEN_WINDOW_FAIL = new ErrorCode(1_007_002_005, "打开传递窗内门失败");
    //启动传递窗内门位皮带输出失败
    ErrorCode TASK_FLOW_STEP_START_WINDOW_IN_BELT_OUT_FAIL = new ErrorCode(1_007_002_006, "启动传递窗内门位皮带输出失败");
    //agv反馈中转罐到位失败
    ErrorCode TASK_FLOW_STEP_AGV_IN_TANK_ARRIVED_FAIL = new ErrorCode(1_007_002_007, "agv反馈中转罐到位失败");
    //关闭传递窗内门失败
    ErrorCode TASK_FLOW_STEP_CLOSE_WINDOW_FAIL = new ErrorCode(1_007_002_008, "关闭传递窗内门失败");
    //下发agv去工站任务失败
    ErrorCode TASK_FLOW_STEP_AGV_GO_TO_STATION_FAIL = new ErrorCode(1_007_002_009, "下发agv去{}任务失败");
    //通知大库单盒开门失败
    ErrorCode TASK_FLOW_STEP_OPEN_SINGLE_BOX_FAIL = new ErrorCode(1_007_002_010, "通知大库单盒开门失败");
    //agv到达大库前置动作执行失败
    ErrorCode AGV_TO_WAREHOUSE_ERROR = new ErrorCode(1_007_002_011,"agv到达大库前置动作执行失败");
    //agv反馈执行失败
    ErrorCode AGV_REPLY_ERROR = new ErrorCode(1_007_002_012,"agv反馈执行失败");
    //大库关门指令下发失败
    ErrorCode TASK_FLOW_STEP_CLOSE_SINGLE_BOX_FAIL = new ErrorCode(1_007_002_013, "大库关门指令下发失败");
    //通知液氮罐准备接罐失败
    ErrorCode TASK_FLOW_STEP_AGV_TO_HC_READY_FAIL = new ErrorCode(1_007_002_014, "通知液氮罐准备接罐失败");


    // ========== 设备报警上传信息 TODO 补充编号 ==========
    ErrorCode IOT_ALARM_NOT_EXISTS = new ErrorCode(1_007_003_014, "设备报警上传信息不存在");

    // ========== 设备状态上传信息 TODO 补充编号 ==========
    ErrorCode TASK_AGV_NOT_ONLINE = new ErrorCode(1_007_010_001, "AGV无空闲设备");

    //TASK_ORDER_PLC_IS_EXECUTING
    ErrorCode TASK_ORDER_PLC_IS_EXECUTING = new ErrorCode(1_007_003_015, "当前任务无法执行，设备正在进行除霜，请等待5-20分钟后再试！");

    // ========== 任务步骤agv日志 TODO 补充编号 ==========
    ErrorCode TASK_FLOW_STEP_AGV_NOT_EXISTS = new ErrorCode(1_007_004_001, "任务步骤agv日志不存在");
    // ========== 任务步骤设备执行日志 TODO 补充编号 ==========
    ErrorCode TASK_FLOW_STEP_LOG_NOT_EXISTS = new ErrorCode(1_007_005_001, "任务步骤设备执行日志不存在");
    // ========== 任务步骤设备执行日志 TODO 补充编号 ==========
    ErrorCode TASK_FLOW_AGV_OUT_NOT_EXISTS = new ErrorCode(1_007_005_002, "任务步骤agv出库日志不存在");
}
