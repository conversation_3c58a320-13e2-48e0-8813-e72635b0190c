package com.hvisions.boot.module.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DeliveryWindowEnum{

    OPEN_LEFT("D1000",1, "调传递舱打开前门"), // 调传递舱打开前门
    ClOSED_LEFT("D1000",2, "调传递舱关团前门"),//调传递舱关团前门
    OPEN_IN("D1000",3, "调传递舱打开内门"),// 调传递舱打开内门
    ClOSED_IN("D1000",4, "调传递舱关闭内门"),//调传递舱关闭内门
    IN_BELT_OUT("D1000", 5,"内门位皮带输出启动"),//内门位皮带输出启动
    IN_BELT_IN("D1000",6, "内门位皮带输入启动"),//内门位皮带输入启动
    OUT_BELT_OUT("D1000",7, "外门位皮带输出启动"),//外门位皮带输出启动
    OUT_BELT_IN("D1000",8, "外门位皮带输入启动"),//外门位皮带输入启动

    OPEN_LEFT_IN("D2000",1, "前门打开执行中"),//前门打开执行中
    IN_BELT_IN_IN("D2000",2, "中转罐放置到位"),//中转罐放置到位
    OPEN_LEFT_OUT("D2000",3, "前门传送关闭中"),//前门传送关闭中
    HUMIDITY("D2000",4, "除湿中"),//除湿中
    IN_OPEN_IN("D2000",5, "内门打开执行中"),//内门打开执行中
    IN_CLOSE_IN("D2000",6, "内门关闭执行中"),//内门关闭执行中
    OUT_TANK_CAN_REMOVE("D2000",7, "外侧中转罐可取出"),// 外侧中转罐可取出
    IN_TANK_CAN_REMOVE("D2000",8,"内侧中转罐可取出"),// 内侧中转罐可取出
    OUT_OPEN_IN("D2000",9, "传递舱消毒中"),//外侧门打开执行中
    OUT_CLOSE_IN("D2000",10, "外门需取罐超时报警"),//外侧门关闭执行中
    OUT_BELT_IN_IN("D2000",11, "内门位皮带输出中"),//外侧中转罐放置到位
    OUT_BELT_OUT_IN("D2000",12, "内门位皮带输入中"),//外侧中转罐传送完成
    OUT_BELT_IN_OUT("D2000",13, "外门位皮带输出中"),//外侧中转罐取出
    OUT_BELT_OUT_OUT("D2000",14, "外门位皮带输入中"),//外侧中转罐传送完成
    //前门打开到位
    OPEN_LEFT_POSITION("D3000",1, "前门打开到位"),
    //前门关闭到位
    CLOSE_LEFT_POSITION("D3000",2, "前门关闭到位"),
    //内门打开到位
    OPEN_IN_POSITION("D3000",3, "内门打开到位"),
    //内门关闭到位
    CLOSE_IN_POSITION("D3000",4, "内门关闭到位"),
    //内门位皮带输出完成
    IN_BELT_OUT_FINISH("D3000",5, "内门位皮带输出完成"),
    //内门位皮带输入完成
    IN_BELT_IN_FINISH("D3000",6, "内门位皮带输入完成"),
    //外门位皮带输出完成
    OUT_BELT_OUT_FINISH("D3000",7, "外门位皮带输出完成"),
    //外门位皮带输入完成
    OUT_BELT_IN_FINISH("D3000",8, "外门位皮带输入完成"),
    ;

    /**
     * 类型
     */
    private final String type;

    /**
     * 值
     */
    private final Integer value;
    /**
     * 类型名
     */
    private final String name;

    public static DeliveryWindowEnum validateDeliveryWindow(String deliveryWindow,Integer actionType) {
        for (DeliveryWindowEnum type : DeliveryWindowEnum.values()) {
            if (type.getType().equals(deliveryWindow) && type.getValue().equals(actionType)) {
                return type;
            }
        }
        throw new IllegalArgumentException("错误类型： " + deliveryWindow);
    }
}
