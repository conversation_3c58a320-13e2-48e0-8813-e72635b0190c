package com.hvisions.boot.module.task.enums.agv;

import com.hvisions.boot.framework.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: TODO
 * @Date: 2025/3/4 22:13
 * @Author: zhangq
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum AgvOrderTypeEnum{

    //1 液氮罐出入库 2 大库/小冰箱入库 3 大库/小冰箱出库 4目的站点皮带向外(送罐) 5.目的站点抓盒回罐 6 目的站点接罐
    LINE_IN("1", "液氮罐出入库"),
    BIG_IN("2", "大库/小冰箱入库"),
    BIG_OUT("3", "大库/小冰箱出库"),
    DESTINATION_OUT("4", "目的站点皮带向外(送罐)"),
    DESTINATION_IN("5", "目的站点抓盒回罐"),
    DESTINATION_PENDING("6", "目的站点接罐"),
    ;
    /**
     * 类型
     */
    private final String status;
    /**
     * 站点
     */
    private final String msg;

    public static AgvOrderTypeEnum validateStatus(Integer status) {
        for (AgvOrderTypeEnum value : AgvOrderTypeEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        throw new ServiceException(500,"agv状态"+status+"不存在");
    }

    public static AgvOrderTypeEnum validateAgvMsg(String msg) {
        for (AgvOrderTypeEnum value : AgvOrderTypeEnum.values()) {
            if (value.getMsg().equals(msg)) {
                return value;
            }
        }
        throw new ServiceException(500,"agv状态描述"+msg+"不存在");
    }
}
