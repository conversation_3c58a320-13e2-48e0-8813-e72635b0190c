package com.hvisions.boot.module.task.enums.agv;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: AGV任务回调枚举
 * @Date: 2025/3/18 14:08
 * @Author: zhangq
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum AgvTaskCallBackEnum {

    //100-任务正常完成
    //481-取货前取消任务
    //482-取货后取消任务
    TASK_COMPLETE("100", "任务正常完成"),
    CANCEL_PICK_UP_OR_UNLOAD_BEFORE("481", "取货前取消任务"),
    CANCEL_PICK_UP_OR_UNLOAD_AFTER("482", "取货后取消任务"),
    //419
    ;
    /**
     * 类型
     */
    private final String code;
    /**
     * 站点
     */
    private final String msg;
}
