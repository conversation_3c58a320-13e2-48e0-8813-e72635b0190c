<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hvisions</groupId>
        <artifactId>dispatch-module-task</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dispatch-module-task-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        freezer 模块
    </description>

    <properties>
        <httpcomponents.version>4.5.14</httpcomponents.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-module-task-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
        </dependency>

        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 工具类相关 -->

        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId> <!-- 实现代码生成 -->
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>dispatch-spring-boot-starter-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId> <!-- 文件客户端：解决 ftp 连接 -->
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId> <!-- 文件客户端：解决 sftp 连接 -->
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId> <!-- 文件客户端：解决阿里云、腾讯云、minio 等 S3 连接 -->
        </dependency>

        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId> <!-- 文件客户端：文件类型的识别 -->
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpcomponents.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>${httpcomponents.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
            <version>2.13.2</version>
        </dependency>

        <dependency>
            <artifactId>dispatch-spring-boot-starter-test</artifactId>
            <groupId>com.hvisions</groupId>
        </dependency>

        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-starter</artifactId>
        </dependency>

    </dependencies>

</project>
