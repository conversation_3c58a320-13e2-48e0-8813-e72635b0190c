package com.hvisions.boot.module.task.controller.admin.freezer.vo;

import com.hvisions.boot.module.task.pojo.Boxes;
import com.hvisions.boot.module.task.pojo.Tubes;
import lombok.Data;

import java.util.List;

/**
 * @Description: 入库单同步
 * @Date: 2024/9/18 10:34
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class FreezerReqVO {

    /**
     * 任务单号
     */
    private String taskCode;

    /**
     * 冻结盒信息
     */
    private List<Boxes> boxes;

    /**
     * 冻存管信息
     */
    private List<Tubes> tubes;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 是否需要agv
     */
    private Boolean enabledAgv;

    /**
     * 任务编号
     */
    private String no;

    /**
     * 预约时间
     */
    private String appointmentTime;
}
