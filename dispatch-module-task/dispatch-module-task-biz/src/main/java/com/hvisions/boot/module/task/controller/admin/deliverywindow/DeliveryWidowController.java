package com.hvisions.boot.module.task.controller.admin.deliverywindow;

import com.hvisions.boot.framework.common.pojo.CommonResult;
import com.hvisions.boot.module.task.controller.admin.deliverywindow.vo.DeliveryWindowSaveReqVO;
import com.hvisions.boot.module.task.service.window.DeliveryWidowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.hvisions.boot.framework.common.pojo.CommonResult.success;

/**
 * @Description: 传递窗
 * @Date: 2024/10/23 10:42
 * @Author: zhangq
 * @Version: 1.0
 */
@RestController
@RequestMapping("/dispatch/deliveryWindow")
@Tag(name = "管理后台 - 传递窗")
@Validated
public class DeliveryWidowController {

    @Resource
    private DeliveryWidowService deliveryWidowService;

    /**
     * D100（1-调传递舱打升前门、2-调传递舱关团前门、3-週传递舱打开内口、4-调传递舱关团内门、5-内门位皮带输出肩动、6-内门位皮带输入肩动、7-外门位皮带输出启动、8-外门位皮带输入肩动）
     * @return
     */
    @PostMapping(value = "/taskprocess")
    @Operation(summary = "传递窗任务执行")
    public CommonResult taskProcess(@Valid @RequestBody DeliveryWindowSaveReqVO saveReqVO){
        return deliveryWidowService.taskProcess(saveReqVO);
    }

    @PostMapping(value = "/getDeliveryWindowStatus")
    @Operation(summary = "传递窗任务当前状态")
    public CommonResult<Integer> getDeliveryWindowStatus(@Valid @RequestBody DeliveryWindowSaveReqVO saveReqVO){
        return success(deliveryWidowService.getDeliveryWindowStatus(saveReqVO));
    }

    /**
     * 传递窗基本信息
     * @return
     */
    @PostMapping(value = "/getDeliveryWindowInfo")
    @Operation(summary = "传递窗基本信息")
    public CommonResult getDeliveryWindowInfo(){
        return success(deliveryWidowService.getDeliveryWindowInfo());
    }
}
