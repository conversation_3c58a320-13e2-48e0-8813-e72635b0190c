package com.hvisions.boot.module.task.controller.admin.deliverywindow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 新增/修改 Request VO")
@Data
public class DeliveryWindowSaveReqVO {

    /**
     * 执行任务对象
     */
    @Schema(description = "执行任务对象")
    private String equipmentCode;

    @Schema(description = "事件代码")
    private String eventCode;

    @Schema(description = "动作类型")
    private Integer actionType;
}