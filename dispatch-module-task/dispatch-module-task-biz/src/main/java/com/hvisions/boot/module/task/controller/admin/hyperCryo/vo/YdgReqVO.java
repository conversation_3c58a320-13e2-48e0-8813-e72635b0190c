package com.hvisions.boot.module.task.controller.admin.hyperCryo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "液氮通知agv执行 Request VO")
@Data
public class YdgReqVO {

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNumber;
    /**
     * 动作类型（可不填）
     */
    @Schema(description = "动作类型（可不填）")
    private String type;
    /**
     * 设备编码
     */
    @Schema(description = "设备编码")
    private String deviceCode;
    /**
     * 任务状态（非必须）
     */
    @Schema(description = "任务状态（非必须）")
    private String taskStatus;
    /**
     * 动作类型
     */
    @Schema(description = "//动作类型  0:默认，1：冰箱盒子出库第一次反馈，2：冰箱出库第二次反馈")
    private Integer actionType;

    @Schema(description ="坐标")
    private List<TubeCoordinate> tubeCoordinates;

}