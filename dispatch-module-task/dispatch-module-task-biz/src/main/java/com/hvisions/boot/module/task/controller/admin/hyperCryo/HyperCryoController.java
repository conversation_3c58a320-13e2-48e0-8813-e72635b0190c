package com.hvisions.boot.module.task.controller.admin.hyperCryo;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.hvisions.boot.framework.common.pojo.CommonResult;
import com.hvisions.boot.framework.common.util.servlet.ServletUtils;
import com.hvisions.boot.module.task.controller.admin.hyperCryo.vo.LiNitrogenTankVO;
import com.hvisions.boot.module.task.controller.admin.hyperCryo.vo.TubeCoordinate;
import com.hvisions.boot.module.task.controller.admin.hyperCryo.vo.YdgReqVO;
import com.hvisions.boot.module.task.service.hyperCryo.HyperCryoService;
import com.hvisions.boot.module.task.utils.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 液氮罐访问调度软件
 * 
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "液氮罐访问调度软件接口管理")
@RestController
@RequestMapping("/dispatch/ydg")
@Validated
public class HyperCryoController {

    @Resource
    private HyperCryoService hyperCryoService;

    /**
     * 1、中转罐进入完成回调指令，出库
     * 2、返回出库完成信息 taskStatus = 1 正常完成
     *    （taskStatus = 2 异常 通常为需要入空盒时候中转罐无盒，盒子扫码失败或者盒子并非空盒）
     *    （taskStatus = 3 异常 扫码与实际情况对不上）
     */
    @PostMapping("/reply/out")
    @Operation(summary = "中转罐进入完成回调指令，出库")
    public CommonResult replyOut(HttpServletRequest request){
        Map<String, String> paramMap = ServletUtils.getParamMap(request);

        String requestBody = ServletUtils.isJsonRequest(request) ? ServletUtils.getBody(request) : null;
        List<TubeCoordinate> tubeCoordinates = new ArrayList<>();
        if (StringUtils.isNotBlank(requestBody)){
            tubeCoordinates = JSON.parseArray(requestBody, TubeCoordinate.class);
        }
        if (CollUtil.isNotEmpty(paramMap)) {
            paramMap.put("tubeCoordinates", requestBody);
            log.info("[液氮罐入库回调]参数request:{}",JSON.toJSONString(paramMap));
            YdgReqVO ydgReqVO = new YdgReqVO();
            if (!paramMap.containsKey("orderNumber")){
                return CommonResult.error(500,"回调参数[orderNumber]不存在，系统无法解析请检查液氮罐系统！");
            }
            String orderNumber = StringUtils.isNotNull(paramMap.get("orderNumber"))?paramMap.get("orderNumber"):null;
            if (orderNumber == null){
                return CommonResult.error(500,"回调参数[orderNumber]值为空，系统无法解析请检查液氮罐系统！");
            }
            ydgReqVO.setOrderNumber(orderNumber);
            if (!paramMap.containsKey("deviceCode")){
                return CommonResult.error(500,"回调参数[deviceCode]不存在，系统无法解析请检查液氮罐系统！");
            }
            String deviceCode = StringUtils.isNotNull(paramMap.get("deviceCode"))?paramMap.get("deviceCode"):null;
            if (deviceCode == null){
                return CommonResult.error(500,"回调参数[deviceCode]值为空，系统无法解析请检查液氮罐系统！");
            }
            ydgReqVO.setDeviceCode(deviceCode);
            if (!paramMap.containsKey("type")){
                return CommonResult.error(500,"回调参数[type]不存在，系统无法解析请检查液氮罐系统！");
            }
            String type = StringUtils.isNotNull(paramMap.get("type"))?paramMap.get("type"):null;
            if (type == null){
                return CommonResult.error(500,"回调参数[type]值为空，系统无法解析请检查液氮罐系统！");
            }
            ydgReqVO.setType(type);
            ydgReqVO.setTaskStatus(StringUtils.isNotNull(paramMap.get("taskStatus"))?paramMap.get("taskStatus"):null);
            ydgReqVO.setTubeCoordinates(tubeCoordinates);
            log.info("中转罐进入完成回调指令:{}", JSON.toJSON(ydgReqVO));
            return hyperCryoService.replyOut(ydgReqVO);
        }else {
            return CommonResult.error(500,"回调参数为空，无法解析请检查液氮罐系统！");
        }
    }

    /**
     * 1、返回预约出库入空盒完成信息（发送f_10后通知AGV机械手拿走中转罐）
     * 2、批量出库中预约出库准备部分已完成指令（发送f_11后可以进行取货操作）
     * @return
     */
    @PostMapping("/reply/out/batch")
    @Operation(summary = "返回预约出库入空盒完成信息")
    public CommonResult replyOutBatch(@RequestParam(value = "orderNumber") String orderNumber,
                                      @RequestParam(value = "deviceCode") String deviceCode,
                                      @RequestParam(value = "type") String type,
                                      @RequestParam(value = "taskStatus", required = false, defaultValue = "") String taskStatus){
        YdgReqVO ydgReqVO = new YdgReqVO();
        ydgReqVO.setOrderNumber(orderNumber);
        ydgReqVO.setDeviceCode(deviceCode);
        ydgReqVO.setType(type);
        ydgReqVO.setTaskStatus(taskStatus);
        log.info("中转罐进入完成回调指令:{}", JSON.toJSON(ydgReqVO));
        //return hyperCryoService.replyOutBatch(ydgReqVO);
        return CommonResult.success(null);
    }

    /**
     * 1、中转罐进入完成回调指令，入库
     * 2、返回入库完成信息 taskStatus = 1 正常完成
     *   （taskStatus = 2 异常 通常为扫码异常或者入库时候中转罐无盒）
     */
    @PostMapping("/reply/in")
    @Operation(summary = "中转罐进入完成回调指令，入库")
    public CommonResult replyIn(HttpServletRequest request){
        Map<String, String> paramMap = ServletUtils.getParamMap(request);

        String requestBody = ServletUtils.isJsonRequest(request) ? ServletUtils.getBody(request) : null;
        List<TubeCoordinate> tubeCoordinates = new ArrayList<>();
        if (StringUtils.isNotBlank(requestBody)){
            tubeCoordinates = JSON.parseArray(requestBody, TubeCoordinate.class);
        }
        if (CollUtil.isNotEmpty(paramMap)) {
            paramMap.put("tubeCoordinates", requestBody);
            log.info("[液氮罐入库回调]参数request:{}",JSON.toJSONString(paramMap));
            YdgReqVO ydgReqVO = new YdgReqVO();
            if (!paramMap.containsKey("orderNumber")){
                return CommonResult.error(500,"回调参数[orderNumber]不存在，系统无法解析请检查液氮罐系统！");
            }
            String orderNumber = StringUtils.isNotNull(paramMap.get("orderNumber"))?paramMap.get("orderNumber"):null;
            if (orderNumber == null){
                return CommonResult.error(500,"回调参数[orderNumber]值为空，系统无法解析请检查液氮罐系统！");
            }
            ydgReqVO.setOrderNumber(orderNumber);
            if (!paramMap.containsKey("deviceCode")){
                return CommonResult.error(500,"回调参数[deviceCode]不存在，系统无法解析请检查液氮罐系统！");
            }
            String deviceCode = StringUtils.isNotNull(paramMap.get("deviceCode"))?paramMap.get("deviceCode"):null;
            if (deviceCode == null){
                return CommonResult.error(500,"回调参数[deviceCode]值为空，系统无法解析请检查液氮罐系统！");
            }
            ydgReqVO.setDeviceCode(deviceCode);
            if (!paramMap.containsKey("type")){
                return CommonResult.error(500,"回调参数[type]不存在，系统无法解析请检查液氮罐系统！");
            }
            String type = StringUtils.isNotNull(paramMap.get("type"))?paramMap.get("type"):null;
            if (type == null){
                return CommonResult.error(500,"回调参数[type]值为空，系统无法解析请检查液氮罐系统！");
            }
            ydgReqVO.setType(type);
            ydgReqVO.setTaskStatus(StringUtils.isNotNull(paramMap.get("taskStatus"))?paramMap.get("taskStatus"):null);
            ydgReqVO.setTubeCoordinates(tubeCoordinates);
            log.info("中转罐进入完成回调指令:{}", JSON.toJSON(ydgReqVO));
            return hyperCryoService.replyIn(ydgReqVO);
        }else {
            return CommonResult.error(500,"回调参数为空，无法解析请检查液氮罐系统！");
        }
    }


    /**
     * 出库指令下发
     */
    @PostMapping("/outbound")
    @Operation(summary = "出库指令下发")
    public CommonResult outbound(@Valid @RequestBody LiNitrogenTankVO liNitrogenTankVO){
        return hyperCryoService.outbound(liNitrogenTankVO);
    }

    /**
     * 入库指令下发
     */
    @PostMapping("/warehousing")
    @Operation(summary = "入库指令下发")
    public CommonResult warehousing(@Valid @RequestBody LiNitrogenTankVO liNitrogenTankVO){
        return hyperCryoService.warehousing(liNitrogenTankVO);
    }

    /**
     * 预约出库耗时查询
     */
    @PostMapping("/bookingTimeQuery")
    @Operation(summary = "预约出库耗时查询")
    public CommonResult outboundTime(@Valid @RequestBody LiNitrogenTankVO liNitrogenTankVO){
        return hyperCryoService.outboundTime(liNitrogenTankVO);
    }

    /**
     * 批量出库指令下发
     */
    @PostMapping("/ScheduledDeliverInstructions")
    @Operation(summary = "批量出库指令下发")
    public CommonResult scheduledDeliverInstructions(@Valid @RequestBody LiNitrogenTankVO liNitrogenTankVO){
        return hyperCryoService.scheduledDeliverInstructions(liNitrogenTankVO);
    }

    /**
     * 取货指令下发
     */
    @PostMapping("/PickupInstructions")
    @Operation(summary = "取货指令下发")
    public CommonResult pickupInstructions(@Valid @RequestBody LiNitrogenTankVO liNitrogenTankVO){
        return hyperCryoService.pickupInstructions(liNitrogenTankVO);
    }

    /**
     * 通知agv执行动作
     */
    @PostMapping("/reply/ydgto/agv")
    @Operation(summary = "通知agv执行动作")
    public CommonResult ydggoAgv(@RequestParam(value = "orderNumber") String orderNumber,
                                 @RequestParam(value = "deviceCode") String deviceCode){
        YdgReqVO ydgReqVO = new YdgReqVO();
        ydgReqVO.setOrderNumber(orderNumber);
        ydgReqVO.setDeviceCode(deviceCode);
        return hyperCryoService.ydggoAgv(ydgReqVO);
    }

    /**
     * AGV到达液氮罐指定位置
     */
    @PostMapping("/reply/agvToHCryo")
    @Operation(summary = "AGV到达液氮罐指定位置-通知液氮罐准备接罐")
    public CommonResult agvToHCryoReady(@RequestParam(value = "destination") String destination){
        return hyperCryoService.agvToHCryoReady(destination);
    }
}
