package com.hvisions.boot.module.task.controller.admin.freezer;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.hvisions.boot.framework.common.pojo.CommonResult;
import com.hvisions.boot.module.task.controller.admin.freezer.vo.FreezerReplyReqVO;
import com.hvisions.boot.module.task.controller.admin.freezer.vo.FreezerReqVO;
import com.hvisions.boot.module.task.service.freezer.FreezerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.hvisions.boot.framework.common.pojo.CommonResult.success;
import static com.hvisions.boot.module.task.enums.DeviceTypeEnum.GIGAULT003;
import static com.hvisions.boot.module.task.enums.DeviceTypeEnum.GIGAULT004;

/**
 * 大冰箱和AGV反馈
 * 
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "大冰箱和AGV反馈接口管理")
@RestController
@RequestMapping("/dispatch/freezer")
@Validated
public class FreezerController {

    @Resource
    private FreezerService freezerService;

      //入库单同步
    @PostMapping(value = "/in")
    @Operation(summary = "入库单同步")
    public CommonResult freezer(@Valid @RequestBody FreezerReqVO freezerReqVO){
        log.info("入库单同步请求参数:{}",JSONObject.toJSONString(freezerReqVO));
        //1.获取地址
       String url = getFreezerUrl(freezerReqVO.getDeviceCode());
        return success(freezerService.inFreezer(freezerReqVO,url));
    }


    @PostMapping(value = "/out")
    @Operation(summary = "出库单同步")
    public CommonResult outFreezer(@Valid @RequestBody FreezerReqVO freezerReqVO){
        log.info("出库单同步请求参数:{}",JSONObject.toJSONString(freezerReqVO));
        //1.获取地址
        String url = getFreezerUrl(freezerReqVO.getDeviceCode());
        return success(freezerService.outFreezer(freezerReqVO,url));
    }

    /**
     * 入库反馈
     */
    @PostMapping("/reply/in")
    @Operation(summary = "入库反馈")
    public CommonResult replyIn(@RequestBody FreezerReplyReqVO replyReqVO){
        return freezerService.replyIn(replyReqVO);
    }

    /**
     * 出库反馈
     */
    @PostMapping("/reply/out")
    @Operation(summary = "出库反馈")
    public CommonResult replyOut(@Valid @RequestBody FreezerReplyReqVO replyReqVO){
        return freezerService.replyOut(replyReqVO);
    }

    /**
     * 打开单盒舱门
     */
    @PostMapping("openSingleBox")
    @Operation(summary = "打开单盒舱门")
    public CommonResult openSingleBox(@Valid @RequestParam(value ="taskCode") String taskCode,
                                      @RequestParam(value = "destination") String destination){
        return freezerService.openSingleBox(taskCode,destination);
    }

    /**
     * 关闭单盒舱门
     */
    @PostMapping("closeSingleBox")
    @Operation(summary = "关闭单盒舱门")
    public CommonResult closeSingleBox(@Valid @RequestParam(value ="taskCode") String taskCode,
                                       @RequestParam(value = "destination") String destination){
        return freezerService.closeSingleBox(taskCode,destination);
    }

    /**
     * 预约出库单同步
     */
    @PostMapping(value = "syncScheduledOutBill")
    @Operation(summary = "预约出库单同步")
    public CommonResult<JSONObject> syncScheduledOutBill(@Valid @RequestBody FreezerReqVO freezerReqVO){
        //1.获取地址
        String url = getFreezerUrl(freezerReqVO.getDeviceCode());
        return success(freezerService.syncScheduledOutBill(freezerReqVO,url));
    }

    /**
     * 取消入库
     */
    @PostMapping("/abortinTask")
    @Operation(summary = "取消入库")
    public CommonResult<JSONObject> abortinTask(@Valid @RequestBody FreezerReqVO replyReqVO){
        return success(freezerService.abortinTask(replyReqVO));
    }

    /**
     * 出库冻存盒到达单盒舱门回调
     * outBoxArriveDoor
     */
    @PostMapping("/outBoxArriveDoor")
    @Operation(summary = "出库冻存盒到达单盒舱门回调")
    public CommonResult<JSONObject> outBoxArriveDoor(@Valid @RequestBody FreezerReplyReqVO replyReqVO){
        return success(freezerService.outBoxArriveDoor(replyReqVO));
    }

    /**
     * 即时出库完毕回调
     * outTaskComplete
     */
    @PostMapping("/outTaskComplete")
    @Operation(summary = "即时出库完毕回调")
    public CommonResult<JSONObject> outTaskComplete(@Valid @RequestBody FreezerReplyReqVO replyReqVO){
        return success(freezerService.outTaskComplete(replyReqVO));
    }

    /**
     * 预约出库完毕回调
     * reserveOutTaskComplete
     */
    @PostMapping("/reserveOutTaskComplete")
    @Operation(summary = "预约出库完毕回调")
    public CommonResult<JSONObject> reserveOutTaskComplete(@Valid @RequestBody FreezerReplyReqVO replyReqVO){
        return success(freezerService.reserveOutTaskComplete(replyReqVO));
    }

    /**
     * 预约取货开始
     * scheduledPickup
     */
    @PostMapping("/scheduledPickup")
    @Operation(summary = "预约取货开始")
    public CommonResult<JSONObject> scheduledPickup(@Valid @RequestBody FreezerReplyReqVO replyReqVO){
        return success(freezerService.scheduledPickup(replyReqVO));
    }


    /**
     * 获取地址
     * @param device
     * @return
     */
    public static String getFreezerUrl(String device) {

        switch (device) {
            case "GigaUlt003":
                return GIGAULT003.getUrl();
            case "GigaUlt004":
                return GIGAULT004.getUrl();
            default:
                throw new IllegalArgumentException(StrUtil.format("未知平台({})", device));
        }
    }
}
