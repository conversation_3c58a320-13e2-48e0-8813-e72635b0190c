package com.hvisions.boot.module.task.controller.admin.freezer.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 入库单同步
 * @Date: 2024/9/18 10:34
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class FreezerReplyReqVO {

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNumber;
    /**
     * 动作类型（可不填）
     */
    @Schema(description = "动作类型")
    private String type;
    /**
     * 设备编码
     */
    @Schema(description = "设备编码")
    private String deviceCode;

    @JSONField(name = "taskCode")
    private String taskCode;

    @JSONField(name = "action")
    private String action;

    @JSONField(name = "boxCode")
    private String boxCode;

    @JSONField(name = "status")
    private Boolean status;

    private List<BoxItem> boxes;
}
