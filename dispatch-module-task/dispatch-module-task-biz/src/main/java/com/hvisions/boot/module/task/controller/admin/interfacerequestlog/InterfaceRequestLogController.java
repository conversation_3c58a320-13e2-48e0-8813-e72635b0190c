package com.hvisions.boot.module.task.controller.admin.interfacerequestlog;

import com.hvisions.boot.framework.apilog.core.annotation.ApiAccessLog;
import com.hvisions.boot.framework.common.pojo.CommonResult;
import com.hvisions.boot.framework.common.pojo.PageParam;
import com.hvisions.boot.framework.common.pojo.PageResult;
import com.hvisions.boot.framework.common.util.object.BeanUtils;
import com.hvisions.boot.framework.excel.core.util.ExcelUtils;
import com.hvisions.boot.module.task.controller.admin.interfacerequestlog.vo.InterfaceRequestLogPageReqVO;
import com.hvisions.boot.module.task.controller.admin.interfacerequestlog.vo.InterfaceRequestLogRespVO;
import com.hvisions.boot.module.task.controller.admin.interfacerequestlog.vo.InterfaceRequestLogSaveReqVO;
import com.hvisions.boot.module.task.dal.dataobject.HvInterfaceRequestLog;
import com.hvisions.boot.module.task.service.interfacerequestlog.InterfaceRequestLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.hvisions.boot.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.hvisions.boot.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 接口请求日志")
@RestController
@RequestMapping("/dispatch/interface-request-log")
@Validated
public class InterfaceRequestLogController {

    @Resource
    private InterfaceRequestLogService interfaceRequestLogService;

    @PostMapping("/create")
    @Operation(summary = "创建接口请求日志")
    @PreAuthorize("@ss.hasPermission('dispatch:interface-request-log:create')")
    public CommonResult<Long> createInterfaceRequestLog(@Valid @RequestBody InterfaceRequestLogSaveReqVO createReqVO) {
        return success(interfaceRequestLogService.createInterfaceRequestLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新接口请求日志")
    @PreAuthorize("@ss.hasPermission('dispatch:interface-request-log:update')")
    public CommonResult<Boolean> updateInterfaceRequestLog(@Valid @RequestBody InterfaceRequestLogSaveReqVO updateReqVO) {
        interfaceRequestLogService.updateInterfaceRequestLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除接口请求日志")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('dispatch:interface-request-log:delete')")
    public CommonResult<Boolean> deleteInterfaceRequestLog(@RequestParam("id") Long id) {
        interfaceRequestLogService.deleteInterfaceRequestLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得接口请求日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('dispatch:interface-request-log:query')")
    public CommonResult<InterfaceRequestLogRespVO> getInterfaceRequestLog(@RequestParam("id") Long id) {
        HvInterfaceRequestLog interfaceRequestLog = interfaceRequestLogService.getInterfaceRequestLog(id);
        return success(BeanUtils.toBean(interfaceRequestLog, InterfaceRequestLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得接口请求日志分页")
    @PreAuthorize("@ss.hasPermission('dispatch:interface-request-log:query')")
    public CommonResult<PageResult<InterfaceRequestLogRespVO>> getInterfaceRequestLogPage(@Valid InterfaceRequestLogPageReqVO pageReqVO) {
        PageResult<HvInterfaceRequestLog> pageResult = interfaceRequestLogService.getInterfaceRequestLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InterfaceRequestLogRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出接口请求日志 Excel")
    @PreAuthorize("@ss.hasPermission('dispatch:interface-request-log:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInterfaceRequestLogExcel(@Valid InterfaceRequestLogPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<HvInterfaceRequestLog> list = interfaceRequestLogService.getInterfaceRequestLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "接口请求日志.xls", "数据", InterfaceRequestLogRespVO.class,
                        BeanUtils.toBean(list, InterfaceRequestLogRespVO.class));
    }

}