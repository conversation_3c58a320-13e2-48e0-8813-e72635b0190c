package com.hvisions.boot.module.task.controller.admin.hyperCryo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 试管列表
 * @Date: 2024/10/10 15:40
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class WarehousingReq {

    /**
     * 冻存管id
     */
    @Schema(description = "冻存管id")
    private String tubeCode;

    @Schema(description = "库位")
    private String targetBox;

}
