package com.hvisions.boot.module.task.controller.admin.interfacerequestlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 接口请求日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InterfaceRequestLogRespVO {

    @Schema(description = "编号，唯一自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "24987")
    @ExcelProperty("编号，唯一自增")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "接口名称", example = "张三")
    @ExcelProperty("接口名称")
    private String apiName;

    @Schema(description = "消费日志")
    @ExcelProperty("消费日志")
    private String consumeLog;

    @Schema(description = "消费状态 1：消费成功 2：消费失败", example = "1")
    @ExcelProperty("消费状态 1：消费成功 2：消费失败")
    private Integer consumeStatus;

    @Schema(description = "异常信息")
    @ExcelProperty("异常信息")
    private String exceptionInfo;

    @Schema(description = "请求host")
    @ExcelProperty("请求host")
    private String host;

    @Schema(description = "是否缺盒 1：缺盒 0：不缺盒", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否缺盒 1：缺盒 0：不缺盒")
    private Integer isLack;

    @Schema(description = "no")
    @ExcelProperty("no")
    private String no;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "请求key")
    @ExcelProperty("请求key")
    private String requestKey;

    @Schema(description = "请求参数")
    @ExcelProperty("请求参数")
    private String requestParam;

    @Schema(description = "响应参数")
    @ExcelProperty("响应参数")
    private String responseParam;

    @Schema(description = "推送状态 1：推送成功 2：推送失败", example = "2")
    @ExcelProperty("推送状态 1：推送成功 2：推送失败")
    private Integer status;

    @Schema(description = "任务状态，1.下发 2.重试 4.已取消", example = "1")
    @ExcelProperty("任务状态，1.下发 2.重试 4.已取消")
    private Integer taskStatus;

    @Schema(description = "唯一标识", example = "32013")
    @ExcelProperty("唯一标识")
    private String uniqueId;

    @Schema(description = "请求url", example = "https://www.iocoder.cn")
    @ExcelProperty("请求url")
    private String url;

}