package com.hvisions.boot.module.task.controller.admin.freezer.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/12/9 14:33
 * @Author: zhangq
 * @Version: 1.0
 */
@Data
public class BoxItem {

    @J<PERSON><PERSON>ield(name = "boxCode")
    private String boxCode;

    @J<PERSON><PERSON>ield(name = "consumableKey")
    private int consumableKey;

    @J<PERSON><PERSON>ield(name = "boxSuccess")
    private Boolean boxSuccess;

    @J<PERSON>NField(name = "locationCode")
    private String locationCode;

    @JSONField(name = "tubes")
    private List<TubeItem> tubes;
}
