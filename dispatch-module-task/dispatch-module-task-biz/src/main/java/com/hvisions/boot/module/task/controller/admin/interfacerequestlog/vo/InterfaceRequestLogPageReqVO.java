package com.hvisions.boot.module.task.controller.admin.interfacerequestlog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.hvisions.boot.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.hvisions.boot.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 接口请求日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InterfaceRequestLogPageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "接口名称", example = "张三")
    private String apiName;

    @Schema(description = "消费日志")
    private String consumeLog;

    @Schema(description = "消费状态 1：消费成功 2：消费失败", example = "1")
    private Integer consumeStatus;

    @Schema(description = "异常信息")
    private String exceptionInfo;

    @Schema(description = "请求host")
    private String host;

    @Schema(description = "是否缺盒 1：缺盒 0：不缺盒")
    private Integer isLack;

    @Schema(description = "no")
    private String no;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "请求key")
    private String requestKey;

    @Schema(description = "请求参数")
    private String requestParam;

    @Schema(description = "响应参数")
    private String responseParam;

    @Schema(description = "推送状态 1：推送成功 2：推送失败", example = "2")
    private Integer status;

    @Schema(description = "任务状态，1.下发 2.重试 4.已取消", example = "1")
    private Integer taskStatus;

    @Schema(description = "唯一标识", example = "32013")
    private String uniqueId;

    @Schema(description = "请求url", example = "https://www.iocoder.cn")
    private String url;

}