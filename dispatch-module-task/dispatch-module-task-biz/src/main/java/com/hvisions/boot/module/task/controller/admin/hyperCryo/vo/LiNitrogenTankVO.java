package com.hvisions.boot.module.task.controller.admin.hyperCryo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: TODO
 * @Date: 2024/10/10 14:32
 * @Author: zhangq
 * @Version: 1.0
 */
@Schema(description = "")
@Data
public class LiNitrogenTankVO {

    /**
     * 设备编码
     */
    @Schema(description = "设备编码")
    private String deviceCode;

    /**
     * 冻存管ID
     */
    //@Schema(description = "冻存管ID")
    //private String tubeCode;

    /**
     * 管理软件单号
     */
    @Schema(description = "管理软件单号")
    private String orderNumber;

    /**
     * agv状态
     */
    @Schema(description = "agv状态(true为启用)")
    private Boolean enableAgv;

    /**
     * 冻存盒编码
     */
    @Schema(description = "冻存盒编码")
    private String boxCode;

    /**
     * 规格id
     */
    @Schema(description = "规格id")
    private String consumableId;

    /**
     * 试管列表
     */
    @Schema(description = "试管列表")
    private List<WarehousingReq> tubeList = new ArrayList<>();

    /**
     * 预约出库时间
     */
    @Schema(description = "预约出库时间")
    private String appointmentTime;

    /**
     *
     */
    private String no;
}
