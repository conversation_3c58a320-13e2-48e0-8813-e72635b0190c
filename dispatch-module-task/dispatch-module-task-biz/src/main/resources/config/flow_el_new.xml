<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="agvFlowOld">
        THEN(
            checkAgvIdle,
            agvTaskMovingIn,
            callbackTask
        );
    </chain>

    <chain name="agvFlow">
        THEN(
          checkAgvIdle,
          checkTransferReady,
          WHEN(
            callbackTask,
            agvTaskMovingIn
          ).ignoreError(true).must(agvTaskMovingIn)
        );
    </chain>

    <chain name="agvToWindowFlow">
        THEN(
            agvArrivedWindowIn,
            windowOpenInnerDoor,
            windowDoorOpenComplete,
            windowOutBeltRunning,<!--这个必须在上一个流程结束后-->
            WHEN(
            THEN(windowOutBeltStop,windowDoorCloseIn),
            agvInTankArrived
            ).must(agvInTankArrived)
        );
    </chain>

    <chain name="freezerIn">
        THEN(
            agvToFreezerIn,
            freezerOpenSingleDoor,
            freezerOpenSingleDoorComplete,
            agvInSingleBoxComplete,
            freezerCloseSingleDoor,
            freezerCloseSingleDoorComplete,
            taskResultComplete,
            SWITCH(freezerInScanResult).TO(
                freezerInSuccess,
                freezerInFail
            )
        );
    </chain>

    <chain name="freezerInSuccess">
        THEN(
        agvMovingWindowOut,
        outTank
        );
    </chain>

    <chain name="freezerInFail">
        THEN(
        freezerInScanFailArrive,
        agvMovingFreezerOut,
        agvToFreezerOut,
        freezerOpenSingleDoor,
        freezerOpenSingleDoorComplete,
        agvOutSingleBoxComplete,
        freezerCloseSingleDoor,
        freezerCloseSingleDoorComplete,
        outTank
        );
    </chain>

    <chain name="outTank">
        THEN(
        agvArrivedWindowOut,
        windowOpenInnerDoor,
        windowDoorOpenComplete,
        windowInBeltRunning,
        WHEN(
            THEN( windowInBeltStop,windowDoorCloseIn),
            agvInWindowComplete
        ),
        callbackTaskResult
        );
    </chain>

    <chain name="freezerOut">
        THEN(
        agvToFreezerOut,
        freezerOpenSingleDoor,
        freezerOpenSingleDoorComplete,
        agvOutSingleBoxComplete,
        freezerCloseSingleDoor,
        freezerCloseSingleDoorComplete,
        taskResultComplete,
        agvMovingWindowOut,
        outTank
        );
    </chain>

    <chain name="hyperCryoIn">
        THEN(
        agvMovingHCryo,
        hCryoTankAgvReady,
        hCryoTankInArrived,
        hCryoTaskComplete,
        hCryoScanResult,
        agvMovingHCryoOut,
        agvMovingHCryoOutComplete,
        hCryoTankAgvReady,
        hCryoTankInAgv,
        outTank
        );
    </chain>

    <chain name="hyperCryoOut">
        THEN(
        agvMovingHCryo,
        hCryoTankAgvReady,
        hCryoTankInArrived,
        hCryoTaskComplete,
        hCryoScanResult,
        agvMovingHCryoOut,
        agvMovingHCryoOutComplete,
        hCryoTankAgvReady,
        hCryoTankInAgv,
        outTank
        );
    </chain>
</flow>
